<template>
  <div class="sidebar" :class="{ collapsed: isCollapsed }">
    <!-- 菜单头 -->
    <div class="menu-header">
      <div class="logo-container" v-if="!isCollapsed">
        <router-link to="/">
          <img src="@/assets/logo.svg" alt="Logo" class="logo" />
        </router-link>
        <span class="app-name">{{ appName }}</span>
      </div>
      <div class="logo-container-collapsed" v-else>
        <router-link to="/">
          <img src="@/assets/logo.svg" alt="Logo" class="logo-small" />
        </router-link>
      </div>
    </div>

    <!-- 顶部菜单项 -->
    <div class="menu-items">
      <router-link
        v-for="item in topMenuItems"
        :key="item.path"
        :to="item.path"
        class="menu-item"
        active-class="active"
      >
        <font-awesome-icon v-if="item.icon" :icon="item.icon" class="menu-icon" />
        <span v-if="!isCollapsed" class="menu-text">{{ item.title }}</span>
      </router-link>
    </div>

    <!-- 底部菜单项 -->
    <div class="menu-bottom">
       <router-link
        v-for="item in bottomMenuItems"
        :key="item.path"
        :to="item.path"
        class="menu-item"
        active-class="active"
      >
        <font-awesome-icon v-if="item.icon" :icon="item.icon" class="menu-icon" />
        <span v-if="!isCollapsed" class="menu-text">{{ item.title }}</span>
      </router-link>

      <!-- 菜单切换按钮 -->
      <div class="menu-toggle-section">
        <div class="menu-toggle" @click="toggleCollapse">
          <font-awesome-icon :icon="isCollapsed ? 'chevron-right' : 'chevron-left'" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, PropType } from 'vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

// 更新 MenuItem 接口，添加可选的 position 属性
export interface MenuItem {
  path: string;
  title: string;
  icon: string;
  position?: 'top' | 'bottom'; // 'top' is default
}

export default defineComponent({
  name: 'MainMenu',
  components: {
    FontAwesomeIcon,
  },
  props: {
    appName: {
      type: String,
      required: true,
    },
    // 将单个 menuItems prop 改为接收一个完整的数组
    items: {
      type: Array as PropType<MenuItem[]>,
      required: true,
    },
  },
  setup(props) {
    const isCollapsed = ref(false);

    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value;
    };

    // 使用计算属性来动态筛选顶部菜单项
    const topMenuItems = computed(() => 
      props.items.filter(item => !item.position || item.position === 'top')
    );

    // 使用计算属性来动态筛选底部菜单项
    const bottomMenuItems = computed(() => 
      props.items.filter(item => item.position === 'bottom')
    );

    return {
      isCollapsed,
      toggleCollapse,
      topMenuItems,
      bottomMenuItems,
    };
  },
});
</script>

<style lang="scss" scoped>
/* Styles remain the same */
.sidebar {
  width: 200px;
  background-color: white;
  color: #333;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
  border-right: 1px solid #e4e7ed;

  &.collapsed {
    width: 60px;
  }

  .menu-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    border-bottom: 1px solid #e4e7ed;
    background-color: white;

    .logo-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .logo {
        width: 36px;
        height: 36px;
      }

      .app-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-right: 40px;
      }
    }

    .logo-container-collapsed {
      display: flex;
      justify-content: center;
      width: 100%;

      .logo-small {
        width: 32px;
        height: 32px;
      }
    }
  }

  .menu-toggle-section {
    padding: 8px 12px;
    border-top: 1px solid #e4e7ed;
    height: 44px;

    .menu-toggle {
      width: 100%;
      height: 36px;
      background-color: #f5f7fa;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      color: #666;
      transition: all 0.3s ease;

      &:hover {
        background-color: #e4e7ed;
        color: #333;
      }
    }
  }

  &.collapsed .menu-toggle-section {
    padding: 8px 6px;

    .menu-toggle {
      height: 32px;
      border-radius: 4px;
    }
  }

  .menu-items {
    flex: 1;
    padding-top: 10px;
  }

  .menu-bottom {
    padding-bottom: 10px;
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    margin: 2px 8px;
    border-radius: 6px;
    height: 44px;

    &:hover {
      background-color: #f5f7fa;
      color: #333;
    }

    &.active {
      background-color: #e6f7ff;
      color: var(--el-color-primary);
      border-left-color: var(--el-color-primary);
    }

    .menu-icon {
      font-size: 16px;
      width: 20px;
      text-align: center;
    }

    .menu-text {
      margin-left: 12px;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      font-weight: 500;
    }
  }

  &.collapsed .menu-item {
    justify-content: center;
    padding: 12px 10px;
    margin: 2px 4px;

    .menu-icon {
      margin: 0;
    }
  }
}
</style>