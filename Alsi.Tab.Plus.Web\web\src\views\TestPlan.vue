<template>
  <div class="app-flex-column">
    <div class="app-card app-space-between">
      <h2>测试计划</h2>
      <el-button type="primary" @click="showCreateDialog">创建测试计划</el-button>
    </div>

    <div class="app-card app-expand">
      <el-table :data="testPlans" style="width: 100%">
        <el-table-column prop="name" label="名称" width="200" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="canoeCfgCategory" label="CANoe CFG" width="200">
          <template #default="scope">
            {{ scope.row.canoeCfgCategory + ' / ' + scope.row.canoeCfgName }}
          </template>
        </el-table-column>
        <el-table-column prop="creationTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.creationTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editTestPlan(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteTestPlan(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3" />
        </el-form-item>
        <el-form-item label="CANoe配置" prop="canoeConfig">
          <el-cascader v-model="form.canoeConfig" :options="canoeOptions" :props="cascaderProps" placeholder="请选择配置"
            style="width: 100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import appApi, { TestPlan, CreateTestPlanRequest, UpdateTestPlanRequest, CanoeCfgOption } from '@/api/appApi'

const testPlans = ref<TestPlan[]>([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentEditId = ref('')

const form = reactive({
  name: '',
  description: '',
  canoeConfig: [] as string[]
})

const canoeOptions = ref<any[]>([])

const cascaderProps = {
  value: 'value',
  label: 'label',
  children: 'children'
}

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  canoeConfig: [{ required: true, message: '请选择CANoe配置', trigger: 'change' }]
}

const formRef = ref()

const dialogTitle = computed(() => isEdit.value ? '编辑测试计划' : '创建测试计划')

const loadTestPlans = async () => {
  try {
    const response = await appApi.testPlan.getList()
    testPlans.value = response.data
  } catch (error) {
    ElMessage.error('加载测试计划失败')
  }
}

const loadCanoeOptions = async () => {
  try {
    const response = await appApi.testPlan.getCanoeCfgOptions()
    canoeOptions.value = response.data.map((option: CanoeCfgOption) => ({
      value: option.category,
      label: option.category,
      children: option.configs.map(config => ({
        value: config,
        label: config
      }))
    }))
  } catch (error) {
    ElMessage.error('加载配置选项失败')
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const editTestPlan = (testPlan: TestPlan) => {
  isEdit.value = true
  currentEditId.value = testPlan.id
  form.name = testPlan.name
  form.description = testPlan.description
  form.canoeConfig = [testPlan.canoeCfgCategory, testPlan.canoeCfgName]
  dialogVisible.value = true
}

const resetForm = () => {
  form.name = ''
  form.description = ''
  form.canoeConfig = []
  formRef.value?.clearValidate()
}

const submitForm = async () => {
  try {
    await formRef.value.validate()

    const [category, name] = form.canoeConfig

    if (isEdit.value) {
      const request: UpdateTestPlanRequest = {
        id: currentEditId.value,
        name: form.name,
        description: form.description,
        canoeCfgCategory: category,
        canoeCfgName: name
      }
      await appApi.testPlan.update(request)
      ElMessage.success('更新成功')
    } else {
      const request: CreateTestPlanRequest = {
        name: form.name,
        description: form.description,
        canoeCfgCategory: category,
        canoeCfgName: name
      }
      await appApi.testPlan.create(request)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadTestPlans()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

const deleteTestPlan = async (testPlan: TestPlan) => {
  try {
    await ElMessageBox.confirm('确定要删除这个测试计划吗？', '确认删除', {
      type: 'warning'
    })

    await appApi.testPlan.delete(testPlan.id)
    ElMessage.success('删除成功')
    loadTestPlans()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const formatDate = (date: Date) => {
  return new Date(date).toLocaleString()
}

onMounted(() => {
  loadTestPlans()
  loadCanoeOptions()
})
</script>

<style scoped>

</style>