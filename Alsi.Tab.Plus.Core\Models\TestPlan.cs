﻿using System;

namespace Alsi.Tab.Plus.Core.Models
{
    public class TestPlan
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string CanoeCfgCategory { get; set; } = string.Empty;
        public string CanoeCfgName { get; set; } = string.Empty;
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }
}
