import { createRouter, createWebHistory, RouteRecordRaw } from "vue-router";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "home",
    component: () => import('@/views/TestPlan.vue'),
  },
  {
    path: '/test-plan',
    name: 'TestPlan',
    component: () => import('@/views/TestPlan.vue'),
    meta: { title: '测试计划' }
  }
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
});

export default router;

