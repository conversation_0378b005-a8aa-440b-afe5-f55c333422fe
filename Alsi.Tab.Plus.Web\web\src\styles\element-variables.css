/* 特别处理弹出菜单的字体，解决CefSharp中字体不一致问题 */
.el-menu--horizontal .el-menu.el-menu--popup .el-menu-item {
  font-family: var(--app-font-family) !important;
  font-size: inherit !important;
  -webkit-font-smoothing: var(--app-font-smoothing-webkit) !important;
  -moz-osx-font-smoothing: var(--app-font-smoothing-moz) !important;
}

/* 修复Element Plus卡片组件滚动条的问题 */
.el-card__body {
  overflow: auto;
}

/* 修复Element Plus菜单分割线的问题 */
.el-divider--horizontal {
  margin: 0;
  padding: 0 10;
}

.el-menu--horizontal .el-menu.el-menu--popup .el-menu-item {
  font-size: 14px !important;
}

.el-sub-menu__title {
  font-size: 14px !important;
}

.el-menu-item.is-active {
  background-color: var(--el-border-color-extra-light);
}

.el-menu-item:hover {
  background-color: #ecf5ff;
}

/* 用例列表组件样式 */
.case-list {

  /* 表头样式 */
  .cases-header {
    .header-row {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
      font-weight: bold;
      font-size: 12px;
      color: #606266;
      padding: 0;
    }

    .header-cell-id {
      width: 60px;
      flex-shrink: 0;
      padding-left: 12px;
    }

    .header-cell-name {
      width: 180px;
      flex-shrink: 0;
      padding-left: 12px;
    }

    .header-cell-param {
      flex: 1;
      padding: 0 10px;
    }

    .header-cell-detail {
      flex: 1;
      padding: 0 10px;
    }

    .header-cell-status {
      width: 80px;
      flex-shrink: 0;
      text-align: center;
      padding-right: 12px;
    }
  }

  /* 用例行样式 */
  .case-row {
    border: none;
    padding: 0;
    cursor: pointer;
    height: 36px;
    line-height: 36px;
    font-size: 12px;
    transition: all 0.3s;
  }

  .case-row-content {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .case-cell-id {
    font-weight: bold;
    color: var(--el-color-primary);
    width: 60px;
    flex-shrink: 0;
    padding-left: 12px;
  }

  .case-cell-name {
    font-weight: 500;
    color: #303133;
    width: 180px;
    flex-shrink: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 0 0 10px;
  }

  .case-cell-param {
    flex: 1;
    color: #606266;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 0 0 10px;
  }

  .case-cell-detail {
    flex: 1;
    padding: 0 0 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #909399;
  }

  .case-cell-status {
    width: 80px;
    flex-shrink: 0;
    text-align: center;
    padding: 0 0 0 10px;
  }

  .case-status-tag {
    min-width: 60px;
    font-size: 12px;
    padding: 0 6px;
    height: 22px;
    line-height: 20px;
  }
}

.el-collapse-item__header {
  border-top: 1px solid #8080802b;
  padding: 0 10px;
}

.el-collapse-item__content {
  margin: 10px;
  padding-bottom: 0;
}

.el-collapse-item__wrap {
  box-shadow: inset 1px 3px 5px rgba(0, 0, 0, .08);
}

.el-timeline-item {
  padding-bottom: 4px;
}

::-webkit-scrollbar {
  width: 10px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.app-base {
  display: flex;
  background: var(--el-fill-color-base);
  height: 100vh;
  width: 100vw;
}

.app-card-container {
  display: flex;
  padding: 16px;
  flex: 1;
}

.app-flex-column {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 16px;
}

.app-flex-row {
  display: flex;
  flex: 1;
  flex-direction: row;
  gap: 16px;
}

.app-card {
  display: flex;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  padding: 16px;
}

.app-expand {
  display: flex;
  flex: 1;
}

.app-space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
