{"version": 3, "file": "js/50.8302ea2a.js", "mappings": "kPAGA,MAAMA,EAAa,CAAEC,MAAO,mBACtBC,EAAa,CAAED,MAAO,8BACtBE,EAAa,CAAEF,MAAO,uBAO5B,OAA4BG,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,WACRC,KAAAA,CAAMC,GC2CR,MAAMC,GAAYC,EAAAA,EAAAA,IAAgB,IAC5BC,GAAgBD,EAAAA,EAAAA,KAAI,GACpBE,GAASF,EAAAA,EAAAA,KAAI,GACbG,GAAgBH,EAAAA,EAAAA,IAAI,IAEpBI,GAAOC,EAAAA,EAAAA,IAAS,CACpBC,KAAM,GACNC,YAAa,GACbC,YAAa,KAGTC,GAAeT,EAAAA,EAAAA,IAAW,IAE1BU,EAAgB,CACpBC,MAAO,QACPC,MAAO,QACPC,SAAU,YAGNC,EAAQ,CACZR,KAAM,CAAC,CAAES,UAAU,EAAMC,QAAS,QAASC,QAAS,SACpDT,YAAa,CAAC,CAAEO,UAAU,EAAMC,QAAS,aAAcC,QAAS,YAG5DC,GAAUlB,EAAAA,EAAAA,MAEVmB,GAAcC,EAAAA,EAAAA,IAAS,IAAMlB,EAAOS,MAAQ,SAAW,UAEvDU,EAAgBC,UACpB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,SAASC,UACvC3B,EAAUY,MAAQY,EAASI,I,CAC3B,MAAOC,GACPC,EAAAA,GAAUD,MAAM,W,GAIdE,EAAmBR,UACvB,IACE,MAAMC,QAAiBC,EAAAA,GAAOC,SAASM,qBACvCtB,EAAaE,MAAQY,EAASI,KAAKK,IAAKC,IAAsB,CAC5DtB,MAAOsB,EAAOC,SACdtB,MAAOqB,EAAOC,SACdrB,SAAUoB,EAAOE,QAAQH,IAAII,IAAU,CACrCzB,MAAOyB,EACPxB,MAAOwB,O,CAGX,MAAOR,GACPC,EAAAA,GAAUD,MAAM,W,GAIdS,EAAmBA,KACvBnC,EAAOS,OAAQ,EACf2B,IACArC,EAAcU,OAAQ,GAGlB4B,EAAgBd,IACpBvB,EAAOS,OAAQ,EACfR,EAAcQ,MAAQc,EAASe,GAC/BpC,EAAKE,KAAOmB,EAASnB,KACrBF,EAAKG,YAAckB,EAASlB,YAC5BH,EAAKI,YAAc,CAACiB,EAASgB,iBAAkBhB,EAASiB,cACxDzC,EAAcU,OAAQ,GAGlB2B,EAAYA,KAChBlC,EAAKE,KAAO,GACZF,EAAKG,YAAc,GACnBH,EAAKI,YAAc,GACnBU,EAAQP,OAAOgC,iBAGXC,EAAatB,UACjB,UACQJ,EAAQP,MAAMkC,WAEpB,MAAOX,EAAU5B,GAAQF,EAAKI,YAE9B,GAAIN,EAAOS,MAAO,CAChB,MAAMmC,EAAiC,CACrCN,GAAIrC,EAAcQ,MAClBL,KAAMF,EAAKE,KACXC,YAAaH,EAAKG,YAClBkC,iBAAkBP,EAClBQ,aAAcpC,SAEVkB,EAAAA,GAAOC,SAASsB,OAAOD,GAC7BjB,EAAAA,GAAUmB,QAAQ,O,KACb,CACL,MAAMF,EAAiC,CACrCxC,KAAMF,EAAKE,KACXC,YAAaH,EAAKG,YAClBkC,iBAAkBP,EAClBQ,aAAcpC,SAEVkB,EAAAA,GAAOC,SAASwB,OAAOH,GAC7BjB,EAAAA,GAAUmB,QAAQ,O,CAGpB/C,EAAcU,OAAQ,EACtBU,G,CACA,MAAOO,GACPC,EAAAA,GAAUD,MAAM1B,EAAOS,MAAQ,OAAS,O,GAItCuC,EAAiB5B,UACrB,UACQ6B,EAAAA,EAAaC,QAAQ,gBAAiB,OAAQ,CAClDC,KAAM,kBAGF7B,EAAAA,GAAOC,SAAS6B,OAAO7B,EAASe,IACtCX,EAAAA,GAAUmB,QAAQ,QAClB3B,G,CACA,MAAOO,GACO,WAAVA,GACFC,EAAAA,GAAUD,MAAM,O,GAKhB2B,EAAcC,GACX,IAAIC,KAAKD,GAAME,iBDjCxB,OCoCAC,EAAAA,EAAAA,IAAU,KACRtC,IACAS,MDtCK,CAAC8B,EAAUC,KAChB,MAAMC,GAAuBC,EAAAA,EAAAA,IAAkB,aACzCC,GAA6BD,EAAAA,EAAAA,IAAkB,mBAC/CE,GAAsBF,EAAAA,EAAAA,IAAkB,YACxCG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAA0BJ,EAAAA,EAAAA,IAAkB,gBAC5CK,GAAyBL,EAAAA,EAAAA,IAAkB,eAC3CM,GAAqBN,EAAAA,EAAAA,IAAkB,WACvCO,GAAuBP,EAAAA,EAAAA,IAAkB,aAE/C,OAAQQ,EAAAA,EAAAA,OC/JRC,EAAAA,EAAAA,IAgDM,MAhDNjF,EAgDM,EA/CJkF,EAAAA,EAAAA,IAGM,MAHNhF,EAGM,CD6JJoE,EAAO,KAAOA,EAAO,IC/JrBY,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRC,EAAAA,EAAAA,IAAsEZ,EAAA,CAA3DT,KAAK,UAAWsB,QAAOtC,GDkK/B,CACDuC,SAASC,EAAAA,EAAAA,ICnKyC,IAAMhB,EAAA,KAAAA,EAAA,KDoKtDiB,EAAAA,EAAAA,ICpKgD,aDsKlDC,EAAG,EACHC,GAAI,CAAC,QCpKTP,EAAAA,EAAAA,IAqBM,MArBN/E,EAqBM,EApBJgF,EAAAA,EAAAA,IAmBWT,EAAA,CAnBAtC,KAAM5B,EAAAY,MAAWsE,MAAA,gBD0KzB,CACDL,SAASC,EAAAA,EAAAA,IC1KT,IAAsD,EAAtDH,EAAAA,EAAAA,IAAsDV,EAAA,CAArCkB,KAAK,OAAOtE,MAAM,KAAKuE,MAAM,SAC9CT,EAAAA,EAAAA,IAAiDV,EAAA,CAAhCkB,KAAK,cAActE,MAAM,QAC1C8D,EAAAA,EAAAA,IAIkBV,EAAA,CAJDkB,KAAK,mBAAmBtE,MAAM,YAAYuE,MAAM,ODsL5D,CCrLQP,SAAOC,EAAAA,EAAAA,IACiDO,GAD1C,EDuLrBN,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,ICtLhBD,EAAME,IAAI7C,iBAAmB,MAAQ2C,EAAME,IAAI5C,cAAY,KDwL9DqC,EAAG,KCrLPL,EAAAA,EAAAA,IAIkBV,EAAA,CAJDkB,KAAK,eAAetE,MAAM,OAAOuE,MAAM,OD2LnD,CC1LQP,SAAOC,EAAAA,EAAAA,IACwBO,GADjB,ED4LrBN,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IC3LhB9B,EAAW6B,EAAME,IAAIC,eAAY,KD6LpCR,EAAG,KC1LPL,EAAAA,EAAAA,IAKkBV,EAAA,CALDpD,MAAM,KAAKuE,MAAM,OD+L7B,CC9LQP,SAAOC,EAAAA,EAAAA,IACuDO,GADhD,EACvBV,EAAAA,EAAAA,IAAuEZ,EAAA,CAA5D0B,KAAK,QAASb,QAAKc,GAAElD,EAAa6C,EAAME,MDkM9C,CACDV,SAASC,EAAAA,EAAAA,ICnM4C,IAAEhB,EAAA,KAAAA,EAAA,KDoMrDiB,EAAAA,EAAAA,ICpMmD,SDsMrDC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,aCvMZN,EAAAA,EAAAA,IAAuFZ,EAAA,CAA5E0B,KAAK,QAAQnC,KAAK,SAAUsB,QAAKc,GAAEvC,EAAekC,EAAME,MD4M9D,CACDV,SAASC,EAAAA,EAAAA,IC7M4D,IAAEhB,EAAA,KAAAA,EAAA,KD8MrEiB,EAAAA,EAAAA,IC9MmE,SDgNrEC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cAEZD,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,YCjNTL,EAAAA,EAAAA,IAiBYJ,EAAA,CAjBAoB,MAAOvE,EAAAR,MDqNjBgF,WCrNuC1F,EAAAU,MDsNvC,sBAAuBkD,EAAO,KAAOA,EAAO,GAAM4B,GCtNXxF,EAAaU,MAAA8E,GAAEN,MAAM,SDwN3D,CC3MUS,QAAMf,EAAAA,EAAAA,IACf,IAAwD,EAAxDH,EAAAA,EAAAA,IAAwDZ,EAAA,CAA5Ca,QAAKd,EAAA,KAAAA,EAAA,GAAA4B,GAAExF,EAAAU,OAAgB,ID8MhC,CACDiE,SAASC,EAAAA,EAAAA,IC/M+B,IAAEhB,EAAA,KAAAA,EAAA,KDgNxCiB,EAAAA,EAAAA,IChNsC,SDkNxCC,EAAG,EACHC,GAAI,CAAC,MClNPN,EAAAA,EAAAA,IAA4DZ,EAAA,CAAjDT,KAAK,UAAWsB,QAAO/B,GDuN/B,CACDgC,SAASC,EAAAA,EAAAA,ICxNmC,IAAEhB,EAAA,MAAAA,EAAA,MDyN5CiB,EAAAA,EAAAA,ICzN0C,SD2N5CC,EAAG,EACHC,GAAI,CAAC,QAGTJ,SAASC,EAAAA,EAAAA,IC7OT,IAWU,EAXVH,EAAAA,EAAAA,IAWUL,EAAA,CAXAwB,MAAOzF,EAAOU,MAAOA,EDiP3BgF,QCjPsC,UAAJ9F,IAAIkB,EAAU,cAAY,SDoP3D,CACD0D,SAASC,EAAAA,EAAAA,ICpPX,IAEe,EAFfH,EAAAA,EAAAA,IAEeP,EAAA,CAFDvD,MAAM,KAAKsE,KAAK,QDwPvB,CACDN,SAASC,EAAAA,EAAAA,ICxPb,IAAgC,EAAhCH,EAAAA,EAAAA,IAAgCR,EAAA,CD0PxByB,WC1PWvF,EAAKE,KD2PhB,sBAAuBuD,EAAO,KAAOA,EAAO,GAAM4B,GC3PvCrF,EAAKE,KAAImF,ID4PnB,KAAM,EAAG,CAAC,iBAEfV,EAAG,KC5PTL,EAAAA,EAAAA,IAEeP,EAAA,CAFDvD,MAAM,KAAKsE,KAAK,eDiQvB,CACDN,SAASC,EAAAA,EAAAA,ICjQb,IAAgE,EAAhEH,EAAAA,EAAAA,IAAgER,EAAA,CDmQxDyB,WCnQWvF,EAAKG,YDoQhB,sBAAuBsD,EAAO,KAAOA,EAAO,GAAM4B,GCpQvCrF,EAAKG,YAAWkF,GAAEpC,KAAK,WAAW0C,KAAK,KDuQjD,KAAM,EAAG,CAAC,iBAEfhB,EAAG,KCvQTL,EAAAA,EAAAA,IAGeP,EAAA,CAHDvD,MAAM,UAAUsE,KAAK,eD4Q5B,CACDN,SAASC,EAAAA,EAAAA,IC5Qb,IACwB,EADxBH,EAAAA,EAAAA,IACwBN,EAAA,CD6QhBuB,WC9QcvF,EAAKI,YD+QnB,sBAAuBqD,EAAO,KAAOA,EAAO,GAAM4B,GC/QpCrF,EAAKI,YAAWiF,GAAGO,QAASvF,EAAAE,MAAesF,MAAOvF,EAAewF,YAAY,QACjGjB,MAAA,gBDmRO,KAAM,EAAG,CAAC,aAAc,cAE7BF,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,YAETA,EAAG,GACF,EAAG,CAAC,QAAS,iBAGpB,IErUA,MAAMoB,EAAc,EAEpB,O", "sources": ["webpack://tab-plus-web/./src/views/TestPlan.vue?51ec", "webpack://tab-plus-web/./src/views/TestPlan.vue", "webpack://tab-plus-web/./src/views/TestPlan.vue?265e"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"app-flex-column\" }\nconst _hoisted_2 = { class: \"app-card app-space-between\" }\nconst _hoisted_3 = { class: \"app-card app-expand\" }\n\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport appApi, { TestPlan, CreateTestPlanRequest, UpdateTestPlanRequest, CanoeCfgOption } from '@/api/appApi'\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestPlan',\n  setup(__props) {\n\nconst testPlans = ref<TestPlan[]>([])\nconst dialogVisible = ref(false)\nconst isEdit = ref(false)\nconst currentEditId = ref('')\n\nconst form = reactive({\n  name: '',\n  description: '',\n  canoeConfig: [] as string[]\n})\n\nconst canoeOptions = ref<any[]>([])\n\nconst cascaderProps = {\n  value: 'value',\n  label: 'label',\n  children: 'children'\n}\n\nconst rules = {\n  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],\n  canoeConfig: [{ required: true, message: '请选择CANoe配置', trigger: 'change' }]\n}\n\nconst formRef = ref()\n\nconst dialogTitle = computed(() => isEdit.value ? '编辑测试计划' : '创建测试计划')\n\nconst loadTestPlans = async () => {\n  try {\n    const response = await appApi.testPlan.getList()\n    testPlans.value = response.data\n  } catch (error) {\n    ElMessage.error('加载测试计划失败')\n  }\n}\n\nconst loadCanoeOptions = async () => {\n  try {\n    const response = await appApi.testPlan.getCanoeCfgOptions()\n    canoeOptions.value = response.data.map((option: CanoeCfgOption) => ({\n      value: option.category,\n      label: option.category,\n      children: option.configs.map(config => ({\n        value: config,\n        label: config\n      }))\n    }))\n  } catch (error) {\n    ElMessage.error('加载配置选项失败')\n  }\n}\n\nconst showCreateDialog = () => {\n  isEdit.value = false\n  resetForm()\n  dialogVisible.value = true\n}\n\nconst editTestPlan = (testPlan: TestPlan) => {\n  isEdit.value = true\n  currentEditId.value = testPlan.id\n  form.name = testPlan.name\n  form.description = testPlan.description\n  form.canoeConfig = [testPlan.canoeCfgCategory, testPlan.canoeCfgName]\n  dialogVisible.value = true\n}\n\nconst resetForm = () => {\n  form.name = ''\n  form.description = ''\n  form.canoeConfig = []\n  formRef.value?.clearValidate()\n}\n\nconst submitForm = async () => {\n  try {\n    await formRef.value.validate()\n\n    const [category, name] = form.canoeConfig\n\n    if (isEdit.value) {\n      const request: UpdateTestPlanRequest = {\n        id: currentEditId.value,\n        name: form.name,\n        description: form.description,\n        canoeCfgCategory: category,\n        canoeCfgName: name\n      }\n      await appApi.testPlan.update(request)\n      ElMessage.success('更新成功')\n    } else {\n      const request: CreateTestPlanRequest = {\n        name: form.name,\n        description: form.description,\n        canoeCfgCategory: category,\n        canoeCfgName: name\n      }\n      await appApi.testPlan.create(request)\n      ElMessage.success('创建成功')\n    }\n\n    dialogVisible.value = false\n    loadTestPlans()\n  } catch (error) {\n    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')\n  }\n}\n\nconst deleteTestPlan = async (testPlan: TestPlan) => {\n  try {\n    await ElMessageBox.confirm('确定要删除这个测试计划吗？', '确认删除', {\n      type: 'warning'\n    })\n\n    await appApi.testPlan.delete(testPlan.id)\n    ElMessage.success('删除成功')\n    loadTestPlans()\n  } catch (error) {\n    if (error !== 'cancel') {\n      ElMessage.error('删除失败')\n    }\n  }\n}\n\nconst formatDate = (date: Date) => {\n  return new Date(date).toLocaleString()\n}\n\nonMounted(() => {\n  loadTestPlans()\n  loadCanoeOptions()\n})\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_form_item = _resolveComponent(\"el-form-item\")!\n  const _component_el_cascader = _resolveComponent(\"el-cascader\")!\n  const _component_el_form = _resolveComponent(\"el-form\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[6] || (_cache[6] = _createElementVNode(\"h2\", null, \"测试计划\", -1)),\n      _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: showCreateDialog\n      }, {\n        default: _withCtx(() => _cache[5] || (_cache[5] = [\n          _createTextVNode(\"创建测试计划\")\n        ])),\n        _: 1,\n        __: [5]\n      })\n    ]),\n    _createElementVNode(\"div\", _hoisted_3, [\n      _createVNode(_component_el_table, {\n        data: testPlans.value,\n        style: {\"width\":\"100%\"}\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_table_column, {\n            prop: \"name\",\n            label: \"名称\",\n            width: \"200\"\n          }),\n          _createVNode(_component_el_table_column, {\n            prop: \"description\",\n            label: \"描述\"\n          }),\n          _createVNode(_component_el_table_column, {\n            prop: \"canoeCfgCategory\",\n            label: \"CANoe CFG\",\n            width: \"200\"\n          }, {\n            default: _withCtx((scope) => [\n              _createTextVNode(_toDisplayString(scope.row.canoeCfgCategory + ' / ' + scope.row.canoeCfgName), 1)\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            prop: \"creationTime\",\n            label: \"创建时间\",\n            width: \"180\"\n          }, {\n            default: _withCtx((scope) => [\n              _createTextVNode(_toDisplayString(formatDate(scope.row.creationTime)), 1)\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_table_column, {\n            label: \"操作\",\n            width: \"200\"\n          }, {\n            default: _withCtx((scope) => [\n              _createVNode(_component_el_button, {\n                size: \"small\",\n                onClick: ($event: any) => (editTestPlan(scope.row))\n              }, {\n                default: _withCtx(() => _cache[7] || (_cache[7] = [\n                  _createTextVNode(\"编辑\")\n                ])),\n                _: 2,\n                __: [7]\n              }, 1032, [\"onClick\"]),\n              _createVNode(_component_el_button, {\n                size: \"small\",\n                type: \"danger\",\n                onClick: ($event: any) => (deleteTestPlan(scope.row))\n              }, {\n                default: _withCtx(() => _cache[8] || (_cache[8] = [\n                  _createTextVNode(\"删除\")\n                ])),\n                _: 2,\n                __: [8]\n              }, 1032, [\"onClick\"])\n            ]),\n            _: 1\n          })\n        ]),\n        _: 1\n      }, 8, [\"data\"])\n    ]),\n    _createVNode(_component_el_dialog, {\n      title: dialogTitle.value,\n      modelValue: dialogVisible.value,\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = ($event: any) => ((dialogVisible).value = $event)),\n      width: \"500px\"\n    }, {\n      footer: _withCtx(() => [\n        _createVNode(_component_el_button, {\n          onClick: _cache[3] || (_cache[3] = ($event: any) => (dialogVisible.value = false))\n        }, {\n          default: _withCtx(() => _cache[9] || (_cache[9] = [\n            _createTextVNode(\"取消\")\n          ])),\n          _: 1,\n          __: [9]\n        }),\n        _createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: submitForm\n        }, {\n          default: _withCtx(() => _cache[10] || (_cache[10] = [\n            _createTextVNode(\"确定\")\n          ])),\n          _: 1,\n          __: [10]\n        })\n      ]),\n      default: _withCtx(() => [\n        _createVNode(_component_el_form, {\n          model: form,\n          rules: rules,\n          ref_key: \"formRef\",\n          ref: formRef,\n          \"label-width\": \"120px\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_form_item, {\n              label: \"名称\",\n              prop: \"name\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_input, {\n                  modelValue: form.name,\n                  \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((form.name) = $event))\n                }, null, 8, [\"modelValue\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"描述\",\n              prop: \"description\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_input, {\n                  modelValue: form.description,\n                  \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((form.description) = $event)),\n                  type: \"textarea\",\n                  rows: \"3\"\n                }, null, 8, [\"modelValue\"])\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_form_item, {\n              label: \"CANoe配置\",\n              prop: \"canoeConfig\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_cascader, {\n                  modelValue: form.canoeConfig,\n                  \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((form.canoeConfig) = $event)),\n                  options: canoeOptions.value,\n                  props: cascaderProps,\n                  placeholder: \"请选择配置\",\n                  style: {\"width\":\"100%\"}\n                }, null, 8, [\"modelValue\", \"options\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _: 1\n        }, 8, [\"model\"])\n      ]),\n      _: 1\n    }, 8, [\"title\", \"modelValue\"])\n  ]))\n}\n}\n\n})", "<template>\n  <div class=\"app-flex-column\">\n    <div class=\"app-card app-space-between\">\n      <h2>测试计划</h2>\n      <el-button type=\"primary\" @click=\"showCreateDialog\">创建测试计划</el-button>\n    </div>\n\n    <div class=\"app-card app-expand\">\n      <el-table :data=\"testPlans\" style=\"width: 100%\">\n        <el-table-column prop=\"name\" label=\"名称\" width=\"200\" />\n        <el-table-column prop=\"description\" label=\"描述\" />\n        <el-table-column prop=\"canoeCfgCategory\" label=\"CANoe CFG\" width=\"200\">\n          <template #default=\"scope\">\n            {{ scope.row.canoeCfgCategory + ' / ' + scope.row.canoeCfgName }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"creationTime\" label=\"创建时间\" width=\"180\">\n          <template #default=\"scope\">\n            {{ formatDate(scope.row.creationTime) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"200\">\n          <template #default=\"scope\">\n            <el-button size=\"small\" @click=\"editTestPlan(scope.row)\">编辑</el-button>\n            <el-button size=\"small\" type=\"danger\" @click=\"deleteTestPlan(scope.row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 创建/编辑对话框 -->\n    <el-dialog :title=\"dialogTitle\" v-model=\"dialogVisible\" width=\"500px\">\n      <el-form :model=\"form\" :rules=\"rules\" ref=\"formRef\" label-width=\"120px\">\n        <el-form-item label=\"名称\" prop=\"name\">\n          <el-input v-model=\"form.name\" />\n        </el-form-item>\n        <el-form-item label=\"描述\" prop=\"description\">\n          <el-input v-model=\"form.description\" type=\"textarea\" rows=\"3\" />\n        </el-form-item>\n        <el-form-item label=\"CANoe配置\" prop=\"canoeConfig\">\n          <el-cascader v-model=\"form.canoeConfig\" :options=\"canoeOptions\" :props=\"cascaderProps\" placeholder=\"请选择配置\"\n            style=\"width: 100%\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport appApi, { TestPlan, CreateTestPlanRequest, UpdateTestPlanRequest, CanoeCfgOption } from '@/api/appApi'\n\nconst testPlans = ref<TestPlan[]>([])\nconst dialogVisible = ref(false)\nconst isEdit = ref(false)\nconst currentEditId = ref('')\n\nconst form = reactive({\n  name: '',\n  description: '',\n  canoeConfig: [] as string[]\n})\n\nconst canoeOptions = ref<any[]>([])\n\nconst cascaderProps = {\n  value: 'value',\n  label: 'label',\n  children: 'children'\n}\n\nconst rules = {\n  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],\n  canoeConfig: [{ required: true, message: '请选择CANoe配置', trigger: 'change' }]\n}\n\nconst formRef = ref()\n\nconst dialogTitle = computed(() => isEdit.value ? '编辑测试计划' : '创建测试计划')\n\nconst loadTestPlans = async () => {\n  try {\n    const response = await appApi.testPlan.getList()\n    testPlans.value = response.data\n  } catch (error) {\n    ElMessage.error('加载测试计划失败')\n  }\n}\n\nconst loadCanoeOptions = async () => {\n  try {\n    const response = await appApi.testPlan.getCanoeCfgOptions()\n    canoeOptions.value = response.data.map((option: CanoeCfgOption) => ({\n      value: option.category,\n      label: option.category,\n      children: option.configs.map(config => ({\n        value: config,\n        label: config\n      }))\n    }))\n  } catch (error) {\n    ElMessage.error('加载配置选项失败')\n  }\n}\n\nconst showCreateDialog = () => {\n  isEdit.value = false\n  resetForm()\n  dialogVisible.value = true\n}\n\nconst editTestPlan = (testPlan: TestPlan) => {\n  isEdit.value = true\n  currentEditId.value = testPlan.id\n  form.name = testPlan.name\n  form.description = testPlan.description\n  form.canoeConfig = [testPlan.canoeCfgCategory, testPlan.canoeCfgName]\n  dialogVisible.value = true\n}\n\nconst resetForm = () => {\n  form.name = ''\n  form.description = ''\n  form.canoeConfig = []\n  formRef.value?.clearValidate()\n}\n\nconst submitForm = async () => {\n  try {\n    await formRef.value.validate()\n\n    const [category, name] = form.canoeConfig\n\n    if (isEdit.value) {\n      const request: UpdateTestPlanRequest = {\n        id: currentEditId.value,\n        name: form.name,\n        description: form.description,\n        canoeCfgCategory: category,\n        canoeCfgName: name\n      }\n      await appApi.testPlan.update(request)\n      ElMessage.success('更新成功')\n    } else {\n      const request: CreateTestPlanRequest = {\n        name: form.name,\n        description: form.description,\n        canoeCfgCategory: category,\n        canoeCfgName: name\n      }\n      await appApi.testPlan.create(request)\n      ElMessage.success('创建成功')\n    }\n\n    dialogVisible.value = false\n    loadTestPlans()\n  } catch (error) {\n    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')\n  }\n}\n\nconst deleteTestPlan = async (testPlan: TestPlan) => {\n  try {\n    await ElMessageBox.confirm('确定要删除这个测试计划吗？', '确认删除', {\n      type: 'warning'\n    })\n\n    await appApi.testPlan.delete(testPlan.id)\n    ElMessage.success('删除成功')\n    loadTestPlans()\n  } catch (error) {\n    if (error !== 'cancel') {\n      ElMessage.error('删除失败')\n    }\n  }\n}\n\nconst formatDate = (date: Date) => {\n  return new Date(date).toLocaleString()\n}\n\nonMounted(() => {\n  loadTestPlans()\n  loadCanoeOptions()\n})\n</script>\n\n<style scoped>\n\n</style>", "import script from \"./TestPlan.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestPlan.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_defineComponent", "__name", "setup", "__props", "testPlans", "ref", "dialogVisible", "isEdit", "currentEditId", "form", "reactive", "name", "description", "canoeConfig", "canoeOptions", "cascaderProps", "value", "label", "children", "rules", "required", "message", "trigger", "formRef", "dialogTitle", "computed", "loadTestPlans", "async", "response", "appApi", "testPlan", "getList", "data", "error", "ElMessage", "loadCanoeOptions", "getCanoeCfgOptions", "map", "option", "category", "configs", "config", "showCreateDialog", "resetForm", "editTestPlan", "id", "canoeCfgCategory", "canoeCfgName", "clearValidate", "submitForm", "validate", "request", "update", "success", "create", "deleteTestPlan", "ElMessageBox", "confirm", "type", "delete", "formatDate", "date", "Date", "toLocaleString", "onMounted", "_ctx", "_cache", "_component_el_button", "_resolveComponent", "_component_el_table_column", "_component_el_table", "_component_el_input", "_component_el_form_item", "_component_el_cascader", "_component_el_form", "_component_el_dialog", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "onClick", "default", "_withCtx", "_createTextVNode", "_", "__", "style", "prop", "width", "scope", "_toDisplayString", "row", "creationTime", "size", "$event", "title", "modelValue", "footer", "model", "ref_key", "rows", "options", "props", "placeholder", "__exports__"], "sourceRoot": ""}