﻿using Alsi.App.Database;
using Alsi.Common.Utils;
using Alsi.Tab.Plus.Core.Models;
using System;

namespace Alsi.Tab.Plus.Core.Services
{
    public class TestPlanService
    {
        private IFreeSql FreeSql => DbEnv.AppDbContext.FreeSql;

        public TestPlan[] GetTestPlans()
        {
            return FreeSql
                .Select<TestPlan>()
                .ToList()
                .ToArray();
        }

        public void CreateTestPlan(string name, string description, string canoeCfgCategory, string canoeCfgName)
        {
            ValidationUtils.ValidateName("Test plan name", name, 100);
            ValidationUtils.ValidateName("Test plan description", description, 1000);
            ValidationUtils.ValidateName("CANoe configuration category", canoeCfgCategory, 100);
            ValidationUtils.ValidateName("CANoe configuration name", canoeCfgName, 100);

            var testPlan = new TestPlan
            {
                Id = Guid.NewGuid(),
                Name = name,
                Description = description ?? string.Empty,
                CanoeCfgCategory = canoeCfgCategory,
                CanoeCfgName = canoeCfgName,
                CreationTime = DateTime.Now
            };

            FreeSql.Insert(testPlan).ExecuteAffrows();
        }

        public void DeleteTestPlan(Guid testPlanId)
        {
            FreeSql.Delete<TestPlan>().Where(x => x.Id == testPlanId).ExecuteAffrows();
        }

        public void UpdateTestPlan(Guid testPlanId, string name, string description)
        {
            ValidationUtils.ValidateName("Test plan name", name, 100);
            ValidationUtils.ValidateName("Test plan description", description, 1000);

            FreeSql.Update<TestPlan>()
                .Set(x => x.Name, name)
                .Set(x => x.Description, description ?? string.Empty)
                .Set(x => x.LastModificationTime, DateTime.Now)
                .Where(x => x.Id == testPlanId)
                .ExecuteAffrows();
        }

        public CanoeCfgOption[] GetCanoeCfgOptions()
        {
            // 硬编码测试数据
            return new[]
            {
                new CanoeCfgOption
                {
                    Category = "Vehicle",
                    Configs = new[] { "Car_Basic", "Car_Advanced", "Truck_Standard" }
                },
                new CanoeCfgOption
                {
                    Category = "Network",
                    Configs = new[] { "CAN_High", "CAN_Low", "FlexRay", "Ethernet" }
                },
                new CanoeCfgOption
                {
                    Category = "Diagnostic",
                    Configs = new[] { "UDS_Basic", "UDS_Extended", "KWP2000" }
                }
            };
        }

        public class CanoeCfgOption
        {
            public string Category { get; set; }
            public string[] Configs { get; set; }
        }
    }
}
