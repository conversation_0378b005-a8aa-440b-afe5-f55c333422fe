[{"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\main.ts": "1", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\api\\appApi.ts": "2", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\App.vue": "3", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\router\\index.ts": "4", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\store\\index.ts": "5", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\views\\TestPlan.vue": "6"}, {"size": 4451, "mtime": 1752562115303, "results": "7", "hashOfConfig": "8"}, {"size": 9697, "mtime": 1752567056126, "results": "9", "hashOfConfig": "8"}, {"size": 561, "mtime": 1752634475836, "results": "10", "hashOfConfig": "8"}, {"size": 480, "mtime": 1752632244575, "results": "11", "hashOfConfig": "8"}, {"size": 145, "mtime": 1752548725913, "results": "12", "hashOfConfig": "8"}, {"size": 5699, "mtime": 1752634575804, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "16"}, "1ufg0ou", {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "19", "usedDeprecatedRules": "16"}, {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "16"}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "16"}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\main.ts", [], [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\api\\appApi.ts", ["28"], "import axios, { AxiosResponse } from 'axios';\r\nimport { sharedAppApi } from 'alsi-shared-web';\r\n\r\n// 定义错误数据结构\r\nexport interface ErrorData {\r\n  message: string;\r\n  stack?: string;\r\n  url: string;\r\n  type?: string;\r\n  vueHookInfo?: string; \r\n  codeInfo?: string;\r\n}\r\n\r\n// 定义应用信息接口\r\nexport interface AppInfo {\r\n  dataFolder: string;\r\n  logFolder: string;\r\n}\r\n\r\n// 定义测试模型接口\r\nexport interface TestMode {\r\n  name: string;\r\n}\r\n\r\n// 数据日志转换相关接口\r\nexport enum DataLogFormat {\r\n  Unknown = 0,\r\n  Asc = 1,\r\n  Blf = 2\r\n}\r\n\r\n\r\n\r\nexport interface DataLogProcessRequest {\r\n  sourceFilePath: string;\r\n  targetFormat: DataLogFormat;\r\n  enableSplit: boolean;\r\n  splitFileCount: number;\r\n}\r\n\r\nexport interface FileProgress {\r\n  fileName: string;\r\n  filePath: string;\r\n  status: ProcessStatus;\r\n  progressPercentage: number;\r\n  errorMessage?: string;\r\n}\r\n\r\nexport interface ProcessProgress {\r\n  taskId: string;\r\n  overallProgressPercentage: number;\r\n  currentOperation: string;\r\n  isCompleted: boolean;\r\n  errorMessage?: string;\r\n  fileProgresses: FileProgress[];\r\n}\r\n\r\nexport enum ProcessStatus {\r\n  Pending = \"Pending\",\r\n  Processing = \"Processing\",\r\n  Completed = \"Completed\",\r\n  Failed = \"Failed\",\r\n  Cancelled = \"Cancelled\"\r\n}\r\n\r\n// App Config 相关接口\r\n\r\nexport enum AppType {\r\n  External = 'External',\r\n  Internal = 'Internal'\r\n}\r\n\r\nexport interface AppEntry {\r\n  id: string;\r\n  appType: AppType;\r\n  exePath: string;\r\n  appUrl: string;\r\n  name: string;\r\n  description: string;\r\n  iconPath: string;\r\n  tags: string[];\r\n  showInTopMenu: boolean;\r\n  showInBottomMenu: boolean;\r\n  displayOrder: number;\r\n  showInHomeCard: boolean;\r\n  icon: string;\r\n  exeExists: boolean;\r\n  iconExists: boolean;\r\n  fullExePath: string;\r\n  workingDirectory: string;\r\n}\r\n\r\nexport interface LaunchAppRequest {\r\n  appId: string;\r\n}\r\n\r\n// CIN 参数工具相关接口\r\nexport interface CinTemplate {\r\n  id: string;\r\n  path: string;\r\n  name: string;\r\n  category: string;\r\n  description: string;\r\n  fullPath: string;\r\n  fileExists: boolean;\r\n}\r\n\r\nexport interface CaplVariable {\r\n  name: string;\r\n  type: string;\r\n  isConst: boolean;\r\n  isArray: boolean;\r\n  arraySize: string;\r\n  value: string;\r\n  originalDeclaration: string;\r\n  lineNumber: number;\r\n  description?: string;\r\n}\r\n\r\nexport interface CinParameterParseRequest {\r\n  sourceType: string;\r\n  templateId?: string;\r\n  filePath: string;\r\n}\r\n\r\nexport interface TypeDefinition {\r\n  typeName: string;\r\n  definition: string;\r\n}\r\n\r\nexport interface CinParameterParseResponse {\r\n  sourceFilePath: string;\r\n  variables: CaplVariable[];\r\n  typeDefinitions: TypeDefinition[];\r\n}\r\n\r\nexport interface CinParameterRequest {\r\n  sourceType: string;\r\n  templateId?: string;\r\n  filePath: string;\r\n  parameterValues: { [key: string]: string };\r\n}\r\n\r\n// 源文件管理相关接口\r\nexport enum SourceFileType {\r\n  Arxml = 'Arxml',\r\n  Sddb = 'Sddb',\r\n  Ldf = 'Ldf'\r\n}\r\n\r\nexport enum SourceFileStatus {\r\n  Pending = 'Pending',\r\n  Parsing = 'Parsing',\r\n  Parsed = 'Parsed',\r\n  Error = 'Error'\r\n}\r\n\r\nexport enum ParamType {\r\n  String = 'String',\r\n  Integer = 'Integer',\r\n  Double = 'Double',\r\n  Boolean = 'Boolean',\r\n  Json = 'Json',\r\n  Array = 'Array',\r\n  Object = 'Object'\r\n}\r\n\r\nexport interface SourceFile {\r\n  id: string;\r\n  path: string;\r\n  fileName: string;\r\n  fileType: SourceFileType;\r\n  status: SourceFileStatus;\r\n  parsedParams: ParsedParam[];\r\n  addTime: Date;\r\n  errorMessage: string;\r\n}\r\n\r\nexport interface ParsedParam {\r\n  ecuName: string;\r\n  name: string;\r\n  value: any;\r\n  paramType: ParamType;\r\n  source: string;\r\n  description: string;\r\n}\r\n\r\nexport interface AddSourceFilesRequest {\r\n  filePaths: string[];\r\n}\r\n\r\nexport interface ParseSourceFileRequest {\r\n  fileId: string;\r\n}\r\n\r\nexport interface RemoveSourceFileRequest {\r\n  fileId: string;\r\n}\r\n\r\nexport interface UserFileHistory {\r\n  lastSelectedPaths: string[];\r\n  lastUpdateTime: Date;\r\n}\r\n\r\nexport interface CinParameterProcessResponse {\r\n  outputFilePath: string;\r\n}\r\n\r\nconst DATALOG_BASE_URL = '/api/DataLogConvert'\r\nconst APP_CONFIG_BASE_URL = '/api/AppConfig'\r\nconst CIN_PARAMETER_BASE_URL = '/api/CinParameter'\r\n\r\n// 测试计划相关接口\r\nexport interface TestPlan {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  canoeCfgCategory: string;\r\n  canoeCfgName: string;\r\n  creationTime: Date;\r\n  lastModificationTime?: Date;\r\n}\r\n\r\nexport interface CreateTestPlanRequest {\r\n  name: string;\r\n  description: string;\r\n  canoeCfgCategory: string;\r\n  canoeCfgName: string;\r\n}\r\n\r\nexport interface UpdateTestPlanRequest {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  canoeCfgCategory: string;\r\n  canoeCfgName: string;\r\n}\r\n\r\nexport interface CanoeCfgOption {\r\n  category: string;\r\n  configs: string[];\r\n}\r\n\r\nconst TESTPLAN_BASE_URL = '/api/TestPlan'\r\n\r\nexport const appApi = {\r\n  ...sharedAppApi,\r\n\r\n  // 数据日志转换相关接口\r\n  dataLogConvert: {\r\n    // 选择文件\r\n    selectFile(): Promise<AxiosResponse<string>> {\r\n      return axios.post(`${DATALOG_BASE_URL}/select-file`);\r\n    },\r\n\r\n    // 开始处理\r\n    startProcess(request: DataLogProcessRequest): Promise<AxiosResponse<string>> {\r\n      return axios.post(`${DATALOG_BASE_URL}/start`, request);\r\n    },\r\n\r\n    // 获取进度\r\n    getProgress(taskId: string): Promise<AxiosResponse<ProcessProgress>> {\r\n      return axios.get(`${DATALOG_BASE_URL}/progress?taskId=${taskId}`);\r\n    },\r\n\r\n    // 取消处理\r\n    cancelProcess(taskId: string): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${DATALOG_BASE_URL}/cancel`, null, { params: { taskId } });\r\n    }\r\n  },\r\n\r\n  // 应用配置相关接口\r\n  appConfig: {\r\n    // 获取应用列表\r\n    getList(): Promise<AxiosResponse<AppEntry[]>> {\r\n      return axios.get(`${APP_CONFIG_BASE_URL}/list`);\r\n    },\r\n\r\n    // 启动应用程序\r\n    launch(request: LaunchAppRequest): Promise<AxiosResponse<{ success: boolean; message: string }>> {\r\n      return axios.post(`${APP_CONFIG_BASE_URL}/launch`, request);\r\n    },\r\n\r\n    // 获取应用图标\r\n    getIconUrl(appId: string): string {\r\n      return `${APP_CONFIG_BASE_URL}/icon?appId=${appId}`;\r\n    }\r\n  },\r\n\r\n  // CIN 参数工具相关接口\r\n  cinParameter: {\r\n    // 获取 CIN 模板列表\r\n    getTemplates(): Promise<AxiosResponse<CinTemplate[]>> {\r\n      return axios.get(`${CIN_PARAMETER_BASE_URL}/templates`);\r\n    },\r\n\r\n    // 选择 CIN 文件\r\n    selectFile(): Promise<AxiosResponse<string>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/select-file`);\r\n    },\r\n\r\n    // 解析 CIN 文件参数\r\n    parseFile(request: CinParameterParseRequest): Promise<AxiosResponse<CinParameterParseResponse>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/parse`, request);\r\n    },\r\n\r\n    // 处理 CIN 文件参数替换\r\n    processFile(request: CinParameterRequest): Promise<AxiosResponse<CinParameterProcessResponse>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/process`, request);\r\n    },\r\n\r\n    // 源文件管理相关接口\r\n\r\n    // 选择源文件\r\n    selectSourceFiles(): Promise<AxiosResponse<string[]>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/select-source-files`);\r\n    },\r\n\r\n    // 添加源文件\r\n    addSourceFiles(request: AddSourceFilesRequest): Promise<AxiosResponse<SourceFile[]>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/add-source-files`, request);\r\n    },\r\n\r\n    // 获取所有源文件\r\n    getSourceFiles(): Promise<AxiosResponse<SourceFile[]>> {\r\n      return axios.get(`${CIN_PARAMETER_BASE_URL}/source-files`);\r\n    },\r\n\r\n    // 解析源文件参数\r\n    parseSourceFile(request: ParseSourceFileRequest): Promise<AxiosResponse<SourceFile>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/parse-source-file`, request);\r\n    },\r\n\r\n    // 移除源文件\r\n    removeSourceFile(request: RemoveSourceFileRequest): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/remove-source-file`, request);\r\n    },\r\n\r\n    // 清空所有源文件\r\n    clearSourceFiles(): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/clear-source-files`);\r\n    },\r\n\r\n    // 获取文件选择历史\r\n    getFileHistory(): Promise<AxiosResponse<UserFileHistory>> {\r\n      return axios.get(`${CIN_PARAMETER_BASE_URL}/file-history`);\r\n    },\r\n\r\n    // 清空文件选择历史\r\n    clearFileHistory(): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/clear-file-history`);\r\n    }\r\n  },\r\n\r\n  // 测试计划相关接口\r\n  testPlan: {\r\n    // 获取测试计划列表\r\n    getList(): Promise<AxiosResponse<TestPlan[]>> {\r\n      return axios.get(`${TESTPLAN_BASE_URL}/list`);\r\n    },\r\n\r\n    // 获取测试计划详情\r\n    getDetail(id: string): Promise<AxiosResponse<TestPlan>> {\r\n      return axios.get(`${TESTPLAN_BASE_URL}/detail?id=${id}`);\r\n    },\r\n\r\n    // 创建测试计划\r\n    create(request: CreateTestPlanRequest): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${TESTPLAN_BASE_URL}/create`, request);\r\n    },\r\n\r\n    // 更新测试计划\r\n    update(request: UpdateTestPlanRequest): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${TESTPLAN_BASE_URL}/update`, request);\r\n    },\r\n\r\n    // 删除测试计划\r\n    delete(id: string): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${TESTPLAN_BASE_URL}/delete`, { id });\r\n    },\r\n\r\n    // 获取CANoe配置选项\r\n    getCanoeCfgOptions(): Promise<AxiosResponse<CanoeCfgOption[]>> {\r\n      return axios.get(`${TESTPLAN_BASE_URL}/canoe-cfg-options`);\r\n    }\r\n  }\r\n}\r\n\r\nexport default appApi\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\App.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\router\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\store\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\views\\TestPlan.vue", ["29"], {"ruleId": "30", "severity": 1, "message": "31", "line": 182, "column": 10, "nodeType": "32", "messageId": "33", "endLine": 182, "endColumn": 13, "suggestions": "34"}, {"ruleId": "30", "severity": 1, "message": "31", "line": 69, "column": 26, "nodeType": "32", "messageId": "33", "endLine": 69, "endColumn": 29, "suggestions": "35"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["36", "37"], ["38", "39"], {"messageId": "40", "fix": "41", "desc": "42"}, {"messageId": "43", "fix": "44", "desc": "45"}, {"messageId": "40", "fix": "46", "desc": "42"}, {"messageId": "43", "fix": "47", "desc": "45"}, "suggestUnknown", {"range": "48", "text": "49"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "48", "text": "50"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "51", "text": "49"}, {"range": "51", "text": "50"}, [3435, 3438], "unknown", "never", [2615, 2618]]