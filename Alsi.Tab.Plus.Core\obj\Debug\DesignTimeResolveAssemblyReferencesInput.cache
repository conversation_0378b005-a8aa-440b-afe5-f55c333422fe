   .winmd.dll.exe    WD:\src\005_TAB\2、Src\1、Source Code\Alsi.Tab.Plus\Alsi.Tab.Plus.Core\packages.confighD:\src\005_TAB\2、Src\1、Source Code\Alsi.Common\Alsi.App.Database\bin\x64\Debug\Alsi.App.Database.dlldD:\src\005_TAB\2、Src\1、Source Code\Alsi.Tab.Plus\packages\FreeSql.3.5.106\lib\net451\FreeSql.dlliC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\Microsoft.CSharp.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\mscorlib.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\System.Core.dllvC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\System.Data.DataSetExtensions.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\System.Data.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\System.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\System.Net.Http.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\System.Xml.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\System.Xml.Linq.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.6.2,AssemblyFoldersEx}
{RawFileName}RD:\src\005_TAB\2、Src\1、Source Code\Alsi.Tab.Plus\Alsi.Tab.Plus.Core\bin\Debug\     D{Registry:Software\Microsoft\.NETFramework,v4.6.2,AssemblyFoldersEx}{D:\src\005_TAB\2、Src\1、Source Code\Alsi.Tab.Plus\Alsi.Tab.Plus.Core\obj\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\Facades\.NETFramework,Version=v4.6.2.NET Framework 4.6.2v4.6.2msil
v4.0.30319         