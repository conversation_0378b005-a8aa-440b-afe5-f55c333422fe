using System;

namespace Alsi.Tab.Plus.Web.Dto
{
    public class TestPlanDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string CanoeCfgCategory { get; set; } = string.Empty;
        public string CanoeCfgName { get; set; } = string.Empty;
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }

    public class CreateTestPlanRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string CanoeCfgCategory { get; set; } = string.Empty;
        public string CanoeCfgName { get; set; } = string.Empty;
    }

    public class UpdateTestPlanRequest
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class DeleteTestPlanRequest
    {
        public Guid Id { get; set; }
    }
}