(function(){"use strict";var e={1021:function(e,n,t){t.d(n,{GQ:function(){return v}});var r,o,i,s,a,c,u=t(4373),l=t(6794);(function(e){e[e["Unknown"]=0]="Unknown",e[e["Asc"]=1]="Asc",e[e["Blf"]=2]="Blf"})(r||(r={})),function(e){e["Pending"]="Pending",e["Processing"]="Processing",e["Completed"]="Completed",e["Failed"]="Failed",e["Cancelled"]="Cancelled"}(o||(o={})),function(e){e["External"]="External",e["Internal"]="Internal"}(i||(i={})),function(e){e["Arxml"]="Arxml",e["Sddb"]="Sddb",e["Ldf"]="Ldf"}(s||(s={})),function(e){e["Pending"]="Pending",e["Parsing"]="Parsing",e["Parsed"]="Parsed",e["Error"]="Error"}(a||(a={})),function(e){e["String"]="String",e["Integer"]="Integer",e["Double"]="Double",e["Boolean"]="Boolean",e["Json"]="Json",e["Array"]="Array",e["Object"]="Object"}(c||(c={}));const f="/api/DataLogConvert",d="/api/AppConfig",p="/api/CinParameter",g="/api/TestPlan",v={...l.vd,dataLogConvert:{selectFile(){return u.A.post(`${f}/select-file`)},startProcess(e){return u.A.post(`${f}/start`,e)},getProgress(e){return u.A.get(`${f}/progress?taskId=${e}`)},cancelProcess(e){return u.A.post(`${f}/cancel`,null,{params:{taskId:e}})}},appConfig:{getList(){return u.A.get(`${d}/list`)},launch(e){return u.A.post(`${d}/launch`,e)},getIconUrl(e){return`${d}/icon?appId=${e}`}},cinParameter:{getTemplates(){return u.A.get(`${p}/templates`)},selectFile(){return u.A.post(`${p}/select-file`)},parseFile(e){return u.A.post(`${p}/parse`,e)},processFile(e){return u.A.post(`${p}/process`,e)},selectSourceFiles(){return u.A.post(`${p}/select-source-files`)},addSourceFiles(e){return u.A.post(`${p}/add-source-files`,e)},getSourceFiles(){return u.A.get(`${p}/source-files`)},parseSourceFile(e){return u.A.post(`${p}/parse-source-file`,e)},removeSourceFile(e){return u.A.post(`${p}/remove-source-file`,e)},clearSourceFiles(){return u.A.post(`${p}/clear-source-files`)},getFileHistory(){return u.A.get(`${p}/file-history`)},clearFileHistory(){return u.A.post(`${p}/clear-file-history`)}},testPlan:{getList(){return u.A.get(`${g}/list`)},getDetail(e){return u.A.get(`${g}/detail?id=${e}`)},create(e){return u.A.post(`${g}/create`,e)},update(e){return u.A.post(`${g}/update`,e)},delete(e){return u.A.post(`${g}/delete`,{id:e})},getCanoeCfgOptions(){return u.A.get(`${g}/canoe-cfg-options`)}}};n.Ay=v},3935:function(e,n,t){e.exports=t.p+"img/logo.36be161e.svg"},5695:function(e,n,t){var r=t(5130),o=t(6768);const i={class:"app-base"},s={class:"app-card-container"};function a(e,n,t,r,a,c){const u=(0,o.g2)("router-view");return(0,o.uX)(),(0,o.CE)("div",i,[(0,o.Lk)("div",s,[(0,o.bF)(u)])])}var c=(0,o.pM)({name:"App",components:{},setup(){return{}}}),u=t(1241);const l=(0,u.A)(c,[["render",a]]);var f=l,d=t(1387);const p=[{path:"/",name:"home",component:()=>t.e(50).then(t.bind(t,1050))},{path:"/test-plan",name:"TestPlan",component:()=>t.e(50).then(t.bind(t,1050)),meta:{title:"测试计划"}}],g=(0,d.aE)({history:(0,d.LA)("/"),routes:p});var v=g,m=t(782),b=(0,m.y$)({state:{},getters:{},mutations:{},actions:{},modules:{}}),h=t(1021),A=t(6794),$=t(7854),y=(t(4188),t(2721)),w=t(7477),P=t(8950),C=t(292),k=t(2353),j=t(4996);P.Yv.add(k.Ubc,k.Uj9,k.QLR,k.h8M,k.LBj,k.Int,k.sjs,k.fny,k.a$,k.ao0,k.$Fj,k.qFF,k.Yj9,k.LqK,k.tdl,k.GF6,k.oZK,k.gr3,k.skf,k.DOu,k.v02,k._qq,k.iW_,k.Wzs,k.XkK,k.pS3,k.ijD,k.APi,k.Vpu,k.MjD,k.cbP,k.yLS,k.jTw,k.y_8,k.Bwz,j.Vz1,j.VGT,k.wXH,k.br3,k.Jn3,k.CLP,k.JOh,k.Q7i,k.Jt7,k.CRT,k.vdG,k.Qzz,k.JXl,k.Qen,k.JC$,k.C0X,k.vmK,k.Qkr,k.JId,k.CIP,k.v6B,k.CnD,k.Jq7,k.COT,k.J7U,k.CtH,k.vRt,k.Qbn,k.Jz$,k.CXX,k.CYq,k.vkd,k.oI1,k.JGI,k.C4u,k.vqh,k.oO5,k.v76,k.CwL,k.JPM);const E=(0,r.Ef)(f);E.component("font-awesome-icon",C.gc);for(const[O,F]of Object.entries(w))E.component(O,F);(0,A.S0)(),E.use(b).use(v).use($.A,{locale:y.A,size:"default"}).mount("#app"),E.config.errorHandler=(e,n,t)=>{console.error("Vue 全局错误:",e);const r={message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:"无堆栈信息",vueHookInfo:t,url:window.location.href};h.GQ.logError(r).catch(e=>{console.error("发送错误到服务器失败:",e)})},window.addEventListener("unhandledrejection",e=>{const n={message:e.reason instanceof Error?e.reason.message:"未处理的Promise异常",stack:e.reason instanceof Error?e.reason.stack:"无堆栈信息",url:window.location.href,type:"unhandledrejection"};h.GQ.logError(n).catch(e=>{console.error("发送Promise错误到服务器失败:",e)})}),window.addEventListener("error",e=>{if(e.message){const n={message:e.message,codeInfo:`${e.filename}:${e.lineno}:${e.colno}`,url:window.location.href,type:"global-error"};h.GQ.logError(n).catch(e=>{console.error("发送全局错误到服务器失败:",e)})}})}},n={};function t(r){var o=n[r];if(void 0!==o)return o.exports;var i=n[r]={exports:{}};return e[r].call(i.exports,i,i.exports,t),i.exports}t.m=e,function(){var e=[];t.O=function(n,r,o,i){if(!r){var s=1/0;for(l=0;l<e.length;l++){r=e[l][0],o=e[l][1],i=e[l][2];for(var a=!0,c=0;c<r.length;c++)(!1&i||s>=i)&&Object.keys(t.O).every(function(e){return t.O[e](r[c])})?r.splice(c--,1):(a=!1,i<s&&(s=i));if(a){e.splice(l--,1);var u=o();void 0!==u&&(n=u)}}return n}i=i||0;for(var l=e.length;l>0&&e[l-1][2]>i;l--)e[l]=e[l-1];e[l]=[r,o,i]}}(),function(){t.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(n,{a:n}),n}}(),function(){t.d=function(e,n){for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})}}(),function(){t.f={},t.e=function(e){return Promise.all(Object.keys(t.f).reduce(function(n,r){return t.f[r](e,n),n},[]))}}(),function(){t.u=function(e){return"js/"+e+".8302ea2a.js"}}(),function(){t.miniCssF=function(e){}}(),function(){t.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)}}(),function(){var e={},n="tab-plus-web:";t.l=function(r,o,i,s){if(e[r])e[r].push(o);else{var a,c;if(void 0!==i)for(var u=document.getElementsByTagName("script"),l=0;l<u.length;l++){var f=u[l];if(f.getAttribute("src")==r||f.getAttribute("data-webpack")==n+i){a=f;break}}a||(c=!0,a=document.createElement("script"),a.charset="utf-8",a.timeout=120,t.nc&&a.setAttribute("nonce",t.nc),a.setAttribute("data-webpack",n+i),a.src=r),e[r]=[o];var d=function(n,t){a.onerror=a.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],a.parentNode&&a.parentNode.removeChild(a),o&&o.forEach(function(e){return e(t)}),n)return n(t)},p=setTimeout(d.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=d.bind(null,a.onerror),a.onload=d.bind(null,a.onload),c&&document.head.appendChild(a)}}}(),function(){t.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){t.p="/"}(),function(){var e={524:0};t.f.j=function(n,r){var o=t.o(e,n)?e[n]:void 0;if(0!==o)if(o)r.push(o[2]);else{var i=new Promise(function(t,r){o=e[n]=[t,r]});r.push(o[2]=i);var s=t.p+t.u(n),a=new Error,c=function(r){if(t.o(e,n)&&(o=e[n],0!==o&&(e[n]=void 0),o)){var i=r&&("load"===r.type?"missing":r.type),s=r&&r.target&&r.target.src;a.message="Loading chunk "+n+" failed.\n("+i+": "+s+")",a.name="ChunkLoadError",a.type=i,a.request=s,o[1](a)}};t.l(s,c,"chunk-"+n,n)}},t.O.j=function(n){return 0===e[n]};var n=function(n,r){var o,i,s=r[0],a=r[1],c=r[2],u=0;if(s.some(function(n){return 0!==e[n]})){for(o in a)t.o(a,o)&&(t.m[o]=a[o]);if(c)var l=c(t)}for(n&&n(r);u<s.length;u++)i=s[u],t.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return t.O(l)},r=self["webpackChunktab_plus_web"]=self["webpackChunktab_plus_web"]||[];r.forEach(n.bind(null,0)),r.push=n.bind(null,r.push.bind(r))}();var r=t.O(void 0,[504],function(){return t(5695)});r=t.O(r)})();
//# sourceMappingURL=app.2f643cd1.js.map