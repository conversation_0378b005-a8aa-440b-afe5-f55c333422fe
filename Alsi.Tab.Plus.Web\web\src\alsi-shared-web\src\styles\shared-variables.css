:root {
  /* ===== 主颜色 - 皇家蓝 #27408B ===== */
  --el-color-primary: #27408B !important;
  --el-color-primary-light-1: #3e55a1 !important;
  /* 轻微变亮 */
  --el-color-primary-light-2: #556bb7 !important;
  --el-color-primary-light-3: #6c80cd !important;
  --el-color-primary-light-4: #8394d8 !important;
  --el-color-primary-light-5: #99a7e3 !important;
  --el-color-primary-light-6: #b0bbee !important;
  --el-color-primary-light-7: #c6cef8 !important;
  --el-color-primary-light-8: #dde2ff !important;
  --el-color-primary-light-9: #eef0ff !important;
  /* 最浅 */
  --el-color-primary-dark-1: #213678 !important;
  /* 轻微变深 */
  --el-color-primary-dark-2: #1b2c65 !important;
  /* 更深 */

  /* ===== 成功色 - 翠绿 ===== */
  /* 选择一个偏青的翠绿色，与蓝色主题形成补充 */
  --el-color-success: #10864D !important;
  --el-color-success-light-1: #269661 !important;
  --el-color-success-light-2: #3ca775 !important;
  --el-color-success-light-3: #52b789 !important;
  --el-color-success-light-4: #68c89d !important;
  --el-color-success-light-5: #7ed9b1 !important;
  --el-color-success-light-6: #94eac5 !important;
  --el-color-success-light-7: #aafbd9 !important;
  --el-color-success-light-8: #c7fcea !important;
  --el-color-success-light-9: #e3fef6 !important;
  --el-color-success-dark-1: #0c6b3e !important;
  --el-color-success-dark-2: #09512F !important;

  /* ===== 警告色 - 琥珀金 ===== */
  /* 选择一个温暖的琥珀色，与蓝色形成良好对比 */
  --el-color-warning: #E67E22 !important;
  --el-color-warning-light-1: #eb8d3a !important;
  --el-color-warning-light-2: #ef9c51 !important;
  --el-color-warning-light-3: #f3ab68 !important;
  --el-color-warning-light-4: #f6ba80 !important;
  --el-color-warning-light-5: #f9c997 !important;
  --el-color-warning-light-6: #fcd8af !important;
  --el-color-warning-light-7: #fee7c6 !important;
  --el-color-warning-light-8: #fff2de !important;
  --el-color-warning-light-9: #fffaf5 !important;
  --el-color-warning-dark-1: #cc6d18 !important;
  --el-color-warning-dark-2: #b35C0F !important;

  /* ===== 危险色 - 深樱桃红 ===== */
  /* 偏暗的红色，更专业、不过于明亮刺眼 */
  --el-color-danger: #C0392B !important;
  --el-color-danger-light-1: #cd4d40 !important;
  --el-color-danger-light-2: #da6154 !important;
  --el-color-danger-light-3: #e77568 !important;
  --el-color-danger-light-4: #f0897d !important;
  --el-color-danger-light-5: #f69d91 !important;
  --el-color-danger-light-6: #fbb1a6 !important;
  --el-color-danger-light-7: #ffc6bb !important;
  --el-color-danger-light-8: #ffdad0 !important;
  --el-color-danger-light-9: #ffeee8 !important;
  --el-color-danger-dark-1: #a3301F !important;
  --el-color-danger-dark-2: #862714 !important;

  /* ===== 信息色 - 中性灰蓝 ===== */
  /* 更中性的灰蓝色，既专业又不与主题色冲突 */
  --el-color-info: #6C7293 !important;
  --el-color-info-light-1: #7a81a2 !important;
  --el-color-info-light-2: #8890b1 !important;
  --el-color-info-light-3: #969fc0 !important;
  --el-color-info-light-4: #a4aecf !important;
  --el-color-info-light-5: #b2bdde !important;
  --el-color-info-light-6: #c0cced !important;
  --el-color-info-light-7: #cedbfc !important;
  --el-color-info-light-8: #dce5ff !important;
  --el-color-info-light-9: #eaefff !important;
  --el-color-info-dark-1: #5d6483 !important;
  --el-color-info-dark-2: #4e5673 !important;

  /* ===== 中性色 - 灰色 ===== */
  --el-text-color-primary: #303133 !important;
  --el-text-color-regular: #606266 !important;
  --el-text-color-secondary: #909399 !important;
  --el-text-color-placeholder: #A8ABB2 !important;
  --el-text-color-disabled: #C0C4CC !important;

  --el-border-color-base: #DCDFE6 !important;
  --el-border-color-light: #E4E7ED !important;
  --el-border-color-lighter: #EBEEF5 !important;
  --el-border-color-extra-light: #F2F6FC !important;

  --el-fill-color-base: #F5F7FA !important;
  --el-fill-color-light: #F5F7FA !important;
  --el-fill-color-lighter: #FAFAFA !important;
  --el-fill-color-extra-light: #FAFCFF !important;
  --el-fill-color-blank: #FFFFFF !important;

  /* ===== 背景色 ===== */
  --el-bg-color: #FFFFFF !important;
  --el-bg-color-overlay: #FFFFFF !important;

  /* ===== 全局字体设置 ===== */
  --app-font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB";
  --app-font-smoothing-webkit: antialiased;
  --app-font-smoothing-moz: grayscale;
}

/* 全局应用字体样式 */
body {
  font-family: var(--app-font-family);
  -webkit-font-smoothing: var(--app-font-smoothing-webkit);
  -moz-osx-font-smoothing: var(--app-font-smoothing-moz);
  font-size: 14px;
}

/* 确保Element Plus组件也使用相同字体 */
.el-menu,
.el-menu-item,
.el-submenu__title,
.el-dropdown-menu,
.el-dropdown-item,
.el-button,
.el-input,
.el-dialog,
.el-card,
.el-message,
.el-notification,
.el-popover,
.el-tooltip,
.el-tabs,
.el-tab-pane,
.el-pagination {
  font-family: var(--app-font-family) !important;
  -webkit-font-smoothing: var(--app-font-smoothing-webkit) !important;
  -moz-osx-font-smoothing: var(--app-font-smoothing-moz) !important;
}
