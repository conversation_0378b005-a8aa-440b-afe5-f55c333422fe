import axios, { AxiosResponse } from 'axios';

// 定义错误数据结构
export interface ErrorData {
  message: string;
  stack?: string;
  url: string;
  type?: string;
  vueHookInfo?: string;
  codeInfo?: string;
}

// 定义应用信息接口
export interface AppInfo {
  dataFolder: string;
  logFolder: string;
}

const BASE_URL = '/api/app'
const EXPLORER_BASE_URL = '/api/explorer'

export const sharedAppApi = {
  // 获取应用信息
  getAppInfo(): Promise<AxiosResponse<AppInfo>> {
    return axios.get(`${BASE_URL}/appInfo`);
  },

  // 记录错误日志
  logError: (errorData: ErrorData) => {
    return axios.post(`${BASE_URL}/logError`, errorData);
  },

  // 退出应用程序
  exit: () => {
    return axios.post(`${BASE_URL}/exit`);
  },

  // 文件资源管理器相关接口
  explorer: {
    // 选择文件夹
    selectFolder(): Promise<AxiosResponse<string>> {
      return axios.get(`${EXPLORER_BASE_URL}/select-folder`);
    },

    // 在资源管理器中显示文件
    openExplorer(path: string): Promise<AxiosResponse<void>> {
      return axios.get(`${EXPLORER_BASE_URL}/open-explorer`, { params: { path } });
    },

    // 打开进程
    startProcess(path: string): Promise<AxiosResponse<void>> {
      return axios.get(`${EXPLORER_BASE_URL}/start-process`, { params: { path } });
    }
  },
}

export default sharedAppApi
