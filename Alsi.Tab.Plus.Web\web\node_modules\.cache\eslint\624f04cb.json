[{"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\main.ts": "1", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\App.vue": "2", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\api\\appApi.ts": "3", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\router\\index.ts": "4", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\store\\index.ts": "5", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\views\\TestPlan.vue": "6"}, {"size": 4451, "mtime": 1752562115303, "results": "7", "hashOfConfig": "8"}, {"size": 561, "mtime": 1752634475836, "results": "9", "hashOfConfig": "8"}, {"size": 9697, "mtime": 1752567056126, "results": "10", "hashOfConfig": "8"}, {"size": 480, "mtime": 1752632244575, "results": "11", "hashOfConfig": "8"}, {"size": 145, "mtime": 1752548725913, "results": "12", "hashOfConfig": "8"}, {"size": 5699, "mtime": 1752634575804, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "16"}, "13fenqg", {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "21"}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\main.ts", ["28", "29", "30", "31"], "import { createApp } from \"vue\";\nimport App from \"./App.vue\";\nimport router from \"./router\";\nimport store from \"./store\";\n\nimport { appApi, type ErrorData } from './api/appApi' // 导入 appApi 而不是 axios\nimport { setupErrorHandler } from 'alsi-shared-web';\n\n// 引入 Element Plus\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport zhCn from 'element-plus/dist/locale/zh-cn.mjs'\n\n// 设置 Element Plus 主题变量\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\n\n// 导入共享变量 CSS 文件\nimport 'alsi-shared-web/src/styles/shared-variables.css'\n\n// 自定义样式\nimport './styles/element-variables.css'\n\n// 引入 FontAwesome\nimport { library } from '@fortawesome/fontawesome-svg-core'\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'\nimport {\n  faCogs, faFolderOpen, faPlus, faFileAlt, faBook,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,\n  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,\n  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,\n  faCode, faEnvelope, faGlobe, faCommentAlt, faCube,\n  faA, faB, faC, faD, faE, faF, faG,\n  faH, faI, faJ, faK, faL, faM, faN,\n  faO, faP, faQ, faR, faS, faT,\n  faU, faV, faW, faX, faY, faZ,\n  fa0, fa1, fa2, fa3, fa4, fa5, fa6, fa7, fa8, fa9\n} from '@fortawesome/free-solid-svg-icons'\n\nimport { faGithub, faTeamspeak } from '@fortawesome/free-brands-svg-icons'\n\n// 添加需要使用的图标到库中\nlibrary.add(\n  faCogs, faFolderOpen, faPlus, faFileAlt, faBook,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter,\n  faHome, faExchangeAlt, faInfoCircle, faChevronLeft, faChevronRight,\n  faEye, faPlay, faStop, faRefresh, faSearch, faDownload, faTrash,\n  faCode, faEnvelope, faGlobe, faGithub, faTeamspeak, faCommentAlt, faCube,\n  faA, faB, faC, faD, faE, faF, faG,\n  faH, faI, faJ, faK, faL, faM, faN,\n  faO, faP, faQ, faR, faS, faT,\n  faU, faV, faW, faX, faY, faZ,\n  fa0, fa1, fa2, fa3, fa4, fa5, fa6, fa7, fa8, fa9\n)\n\nconst app = createApp(App)\n\n// 全局注册 FontAwesome 组件\napp.component('font-awesome-icon', FontAwesomeIcon)\n\n// 全局注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\n// 设置全局错误处理\nsetupErrorHandler()\n\napp.use(store)\n   .use(router)\n   .use(ElementPlus, {\n     locale: zhCn,\n     size: 'default'\n   })\n   .mount('#app')\n\n// 定义 sendError 类型\ntype SendErrorType = Error | unknown;\n\n// 全局异常处理\napp.config.errorHandler = (err: unknown, vm, info) => {\n  // 控制台输出错误\n  console.error(\"Vue 全局错误:\", err);\n\n  // 将错误发送到后端\n  const errorData: ErrorData = {\n    message: err instanceof Error ? err.message : String(err),\n    stack: err instanceof Error ? err.stack : \"无堆栈信息\",\n    vueHookInfo: info, // 更新字段名\n    url: window.location.href,\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送错误到服务器失败:\", sendError);\n  });\n};\n\n// 捕获未处理的Promise异常\nwindow.addEventListener(\"unhandledrejection\", (event) => {\n  const errorData: ErrorData = {\n    message:\n      event.reason instanceof Error\n        ? event.reason.message\n        : \"未处理的Promise异常\",\n    stack: event.reason instanceof Error ? event.reason.stack : \"无堆栈信息\",\n    url: window.location.href,\n    type: \"unhandledrejection\",\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送Promise错误到服务器失败:\", sendError);\n  });\n});\n\n// 捕获全局JS错误\nwindow.addEventListener(\"error\", (event) => {\n  // 过滤资源加载错误\n  if (event.message) {\n    const errorData: ErrorData = {\n      message: event.message,\n      codeInfo: `${event.filename}:${event.lineno}:${event.colno}`,\n      url: window.location.href,\n      type: \"global-error\",\n    };\n\n    appApi.logError(errorData).catch((sendError: SendErrorType) => {\n      console.error(\"发送全局错误到服务器失败:\", sendError);\n    });\n  }\n});\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\App.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\api\\appApi.ts", ["32"], "import axios, { AxiosResponse } from 'axios';\r\nimport { sharedAppApi } from 'alsi-shared-web';\r\n\r\n// 定义错误数据结构\r\nexport interface ErrorData {\r\n  message: string;\r\n  stack?: string;\r\n  url: string;\r\n  type?: string;\r\n  vueHookInfo?: string; \r\n  codeInfo?: string;\r\n}\r\n\r\n// 定义应用信息接口\r\nexport interface AppInfo {\r\n  dataFolder: string;\r\n  logFolder: string;\r\n}\r\n\r\n// 定义测试模型接口\r\nexport interface TestMode {\r\n  name: string;\r\n}\r\n\r\n// 数据日志转换相关接口\r\nexport enum DataLogFormat {\r\n  Unknown = 0,\r\n  Asc = 1,\r\n  Blf = 2\r\n}\r\n\r\n\r\n\r\nexport interface DataLogProcessRequest {\r\n  sourceFilePath: string;\r\n  targetFormat: DataLogFormat;\r\n  enableSplit: boolean;\r\n  splitFileCount: number;\r\n}\r\n\r\nexport interface FileProgress {\r\n  fileName: string;\r\n  filePath: string;\r\n  status: ProcessStatus;\r\n  progressPercentage: number;\r\n  errorMessage?: string;\r\n}\r\n\r\nexport interface ProcessProgress {\r\n  taskId: string;\r\n  overallProgressPercentage: number;\r\n  currentOperation: string;\r\n  isCompleted: boolean;\r\n  errorMessage?: string;\r\n  fileProgresses: FileProgress[];\r\n}\r\n\r\nexport enum ProcessStatus {\r\n  Pending = \"Pending\",\r\n  Processing = \"Processing\",\r\n  Completed = \"Completed\",\r\n  Failed = \"Failed\",\r\n  Cancelled = \"Cancelled\"\r\n}\r\n\r\n// App Config 相关接口\r\n\r\nexport enum AppType {\r\n  External = 'External',\r\n  Internal = 'Internal'\r\n}\r\n\r\nexport interface AppEntry {\r\n  id: string;\r\n  appType: AppType;\r\n  exePath: string;\r\n  appUrl: string;\r\n  name: string;\r\n  description: string;\r\n  iconPath: string;\r\n  tags: string[];\r\n  showInTopMenu: boolean;\r\n  showInBottomMenu: boolean;\r\n  displayOrder: number;\r\n  showInHomeCard: boolean;\r\n  icon: string;\r\n  exeExists: boolean;\r\n  iconExists: boolean;\r\n  fullExePath: string;\r\n  workingDirectory: string;\r\n}\r\n\r\nexport interface LaunchAppRequest {\r\n  appId: string;\r\n}\r\n\r\n// CIN 参数工具相关接口\r\nexport interface CinTemplate {\r\n  id: string;\r\n  path: string;\r\n  name: string;\r\n  category: string;\r\n  description: string;\r\n  fullPath: string;\r\n  fileExists: boolean;\r\n}\r\n\r\nexport interface CaplVariable {\r\n  name: string;\r\n  type: string;\r\n  isConst: boolean;\r\n  isArray: boolean;\r\n  arraySize: string;\r\n  value: string;\r\n  originalDeclaration: string;\r\n  lineNumber: number;\r\n  description?: string;\r\n}\r\n\r\nexport interface CinParameterParseRequest {\r\n  sourceType: string;\r\n  templateId?: string;\r\n  filePath: string;\r\n}\r\n\r\nexport interface TypeDefinition {\r\n  typeName: string;\r\n  definition: string;\r\n}\r\n\r\nexport interface CinParameterParseResponse {\r\n  sourceFilePath: string;\r\n  variables: CaplVariable[];\r\n  typeDefinitions: TypeDefinition[];\r\n}\r\n\r\nexport interface CinParameterRequest {\r\n  sourceType: string;\r\n  templateId?: string;\r\n  filePath: string;\r\n  parameterValues: { [key: string]: string };\r\n}\r\n\r\n// 源文件管理相关接口\r\nexport enum SourceFileType {\r\n  Arxml = 'Arxml',\r\n  Sddb = 'Sddb',\r\n  Ldf = 'Ldf'\r\n}\r\n\r\nexport enum SourceFileStatus {\r\n  Pending = 'Pending',\r\n  Parsing = 'Parsing',\r\n  Parsed = 'Parsed',\r\n  Error = 'Error'\r\n}\r\n\r\nexport enum ParamType {\r\n  String = 'String',\r\n  Integer = 'Integer',\r\n  Double = 'Double',\r\n  Boolean = 'Boolean',\r\n  Json = 'Json',\r\n  Array = 'Array',\r\n  Object = 'Object'\r\n}\r\n\r\nexport interface SourceFile {\r\n  id: string;\r\n  path: string;\r\n  fileName: string;\r\n  fileType: SourceFileType;\r\n  status: SourceFileStatus;\r\n  parsedParams: ParsedParam[];\r\n  addTime: Date;\r\n  errorMessage: string;\r\n}\r\n\r\nexport interface ParsedParam {\r\n  ecuName: string;\r\n  name: string;\r\n  value: any;\r\n  paramType: ParamType;\r\n  source: string;\r\n  description: string;\r\n}\r\n\r\nexport interface AddSourceFilesRequest {\r\n  filePaths: string[];\r\n}\r\n\r\nexport interface ParseSourceFileRequest {\r\n  fileId: string;\r\n}\r\n\r\nexport interface RemoveSourceFileRequest {\r\n  fileId: string;\r\n}\r\n\r\nexport interface UserFileHistory {\r\n  lastSelectedPaths: string[];\r\n  lastUpdateTime: Date;\r\n}\r\n\r\nexport interface CinParameterProcessResponse {\r\n  outputFilePath: string;\r\n}\r\n\r\nconst DATALOG_BASE_URL = '/api/DataLogConvert'\r\nconst APP_CONFIG_BASE_URL = '/api/AppConfig'\r\nconst CIN_PARAMETER_BASE_URL = '/api/CinParameter'\r\n\r\n// 测试计划相关接口\r\nexport interface TestPlan {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  canoeCfgCategory: string;\r\n  canoeCfgName: string;\r\n  creationTime: Date;\r\n  lastModificationTime?: Date;\r\n}\r\n\r\nexport interface CreateTestPlanRequest {\r\n  name: string;\r\n  description: string;\r\n  canoeCfgCategory: string;\r\n  canoeCfgName: string;\r\n}\r\n\r\nexport interface UpdateTestPlanRequest {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  canoeCfgCategory: string;\r\n  canoeCfgName: string;\r\n}\r\n\r\nexport interface CanoeCfgOption {\r\n  category: string;\r\n  configs: string[];\r\n}\r\n\r\nconst TESTPLAN_BASE_URL = '/api/TestPlan'\r\n\r\nexport const appApi = {\r\n  ...sharedAppApi,\r\n\r\n  // 数据日志转换相关接口\r\n  dataLogConvert: {\r\n    // 选择文件\r\n    selectFile(): Promise<AxiosResponse<string>> {\r\n      return axios.post(`${DATALOG_BASE_URL}/select-file`);\r\n    },\r\n\r\n    // 开始处理\r\n    startProcess(request: DataLogProcessRequest): Promise<AxiosResponse<string>> {\r\n      return axios.post(`${DATALOG_BASE_URL}/start`, request);\r\n    },\r\n\r\n    // 获取进度\r\n    getProgress(taskId: string): Promise<AxiosResponse<ProcessProgress>> {\r\n      return axios.get(`${DATALOG_BASE_URL}/progress?taskId=${taskId}`);\r\n    },\r\n\r\n    // 取消处理\r\n    cancelProcess(taskId: string): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${DATALOG_BASE_URL}/cancel`, null, { params: { taskId } });\r\n    }\r\n  },\r\n\r\n  // 应用配置相关接口\r\n  appConfig: {\r\n    // 获取应用列表\r\n    getList(): Promise<AxiosResponse<AppEntry[]>> {\r\n      return axios.get(`${APP_CONFIG_BASE_URL}/list`);\r\n    },\r\n\r\n    // 启动应用程序\r\n    launch(request: LaunchAppRequest): Promise<AxiosResponse<{ success: boolean; message: string }>> {\r\n      return axios.post(`${APP_CONFIG_BASE_URL}/launch`, request);\r\n    },\r\n\r\n    // 获取应用图标\r\n    getIconUrl(appId: string): string {\r\n      return `${APP_CONFIG_BASE_URL}/icon?appId=${appId}`;\r\n    }\r\n  },\r\n\r\n  // CIN 参数工具相关接口\r\n  cinParameter: {\r\n    // 获取 CIN 模板列表\r\n    getTemplates(): Promise<AxiosResponse<CinTemplate[]>> {\r\n      return axios.get(`${CIN_PARAMETER_BASE_URL}/templates`);\r\n    },\r\n\r\n    // 选择 CIN 文件\r\n    selectFile(): Promise<AxiosResponse<string>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/select-file`);\r\n    },\r\n\r\n    // 解析 CIN 文件参数\r\n    parseFile(request: CinParameterParseRequest): Promise<AxiosResponse<CinParameterParseResponse>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/parse`, request);\r\n    },\r\n\r\n    // 处理 CIN 文件参数替换\r\n    processFile(request: CinParameterRequest): Promise<AxiosResponse<CinParameterProcessResponse>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/process`, request);\r\n    },\r\n\r\n    // 源文件管理相关接口\r\n\r\n    // 选择源文件\r\n    selectSourceFiles(): Promise<AxiosResponse<string[]>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/select-source-files`);\r\n    },\r\n\r\n    // 添加源文件\r\n    addSourceFiles(request: AddSourceFilesRequest): Promise<AxiosResponse<SourceFile[]>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/add-source-files`, request);\r\n    },\r\n\r\n    // 获取所有源文件\r\n    getSourceFiles(): Promise<AxiosResponse<SourceFile[]>> {\r\n      return axios.get(`${CIN_PARAMETER_BASE_URL}/source-files`);\r\n    },\r\n\r\n    // 解析源文件参数\r\n    parseSourceFile(request: ParseSourceFileRequest): Promise<AxiosResponse<SourceFile>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/parse-source-file`, request);\r\n    },\r\n\r\n    // 移除源文件\r\n    removeSourceFile(request: RemoveSourceFileRequest): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/remove-source-file`, request);\r\n    },\r\n\r\n    // 清空所有源文件\r\n    clearSourceFiles(): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/clear-source-files`);\r\n    },\r\n\r\n    // 获取文件选择历史\r\n    getFileHistory(): Promise<AxiosResponse<UserFileHistory>> {\r\n      return axios.get(`${CIN_PARAMETER_BASE_URL}/file-history`);\r\n    },\r\n\r\n    // 清空文件选择历史\r\n    clearFileHistory(): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${CIN_PARAMETER_BASE_URL}/clear-file-history`);\r\n    }\r\n  },\r\n\r\n  // 测试计划相关接口\r\n  testPlan: {\r\n    // 获取测试计划列表\r\n    getList(): Promise<AxiosResponse<TestPlan[]>> {\r\n      return axios.get(`${TESTPLAN_BASE_URL}/list`);\r\n    },\r\n\r\n    // 获取测试计划详情\r\n    getDetail(id: string): Promise<AxiosResponse<TestPlan>> {\r\n      return axios.get(`${TESTPLAN_BASE_URL}/detail?id=${id}`);\r\n    },\r\n\r\n    // 创建测试计划\r\n    create(request: CreateTestPlanRequest): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${TESTPLAN_BASE_URL}/create`, request);\r\n    },\r\n\r\n    // 更新测试计划\r\n    update(request: UpdateTestPlanRequest): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${TESTPLAN_BASE_URL}/update`, request);\r\n    },\r\n\r\n    // 删除测试计划\r\n    delete(id: string): Promise<AxiosResponse<{ success: boolean }>> {\r\n      return axios.post(`${TESTPLAN_BASE_URL}/delete`, { id });\r\n    },\r\n\r\n    // 获取CANoe配置选项\r\n    getCanoeCfgOptions(): Promise<AxiosResponse<CanoeCfgOption[]>> {\r\n      return axios.get(`${TESTPLAN_BASE_URL}/canoe-cfg-options`);\r\n    }\r\n  }\r\n}\r\n\r\nexport default appApi\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\router\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\store\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Web\\web\\src\\views\\TestPlan.vue", ["33"], {"ruleId": "34", "severity": 1, "message": "35", "line": 90, "column": 3, "nodeType": "36", "messageId": "37", "endLine": 90, "endColumn": 16}, {"ruleId": "34", "severity": 1, "message": "35", "line": 101, "column": 5, "nodeType": "36", "messageId": "37", "endLine": 101, "endColumn": 18}, {"ruleId": "34", "severity": 1, "message": "35", "line": 118, "column": 5, "nodeType": "36", "messageId": "37", "endLine": 118, "endColumn": 18}, {"ruleId": "34", "severity": 1, "message": "35", "line": 134, "column": 7, "nodeType": "36", "messageId": "37", "endLine": 134, "endColumn": 20}, {"ruleId": "38", "severity": 1, "message": "39", "line": 182, "column": 10, "nodeType": "40", "messageId": "41", "endLine": 182, "endColumn": 13, "suggestions": "42"}, {"ruleId": "38", "severity": 1, "message": "39", "line": 69, "column": 26, "nodeType": "40", "messageId": "41", "endLine": 69, "endColumn": 29, "suggestions": "43"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["44", "45"], ["46", "47"], {"messageId": "48", "fix": "49", "desc": "50"}, {"messageId": "51", "fix": "52", "desc": "53"}, {"messageId": "48", "fix": "54", "desc": "50"}, {"messageId": "51", "fix": "55", "desc": "53"}, "suggestUnknown", {"range": "56", "text": "57"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "56", "text": "58"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "59", "text": "57"}, {"range": "59", "text": "58"}, [3435, 3438], "unknown", "never", [2615, 2618]]