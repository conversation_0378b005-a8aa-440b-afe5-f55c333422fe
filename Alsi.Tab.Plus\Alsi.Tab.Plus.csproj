<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{EBE7FB51-210F-47AF-8365-D30B6FA1030F}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Alsi.Tab.Plus</RootNamespace>
    <AssemblyName>Alsi.Tab.Plus</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\bin\Debug\x64\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>..\bin\Debug\x64\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="FreeSql, Version=3.5.106.0, Culture=neutral, PublicKeyToken=a33928e5d4a4b39c, processorArchitecture=MSIL">
      <HintPath>..\packages\FreeSql.3.5.106\lib\net451\FreeSql.dll</HintPath>
    </Reference>
    <Reference Include="FreeSql.Provider.Sqlite, Version=3.5.106.0, Culture=neutral, PublicKeyToken=5800863e689c9dd8, processorArchitecture=MSIL">
      <HintPath>..\packages\FreeSql.Provider.Sqlite.3.5.106\lib\net45\FreeSql.Provider.Sqlite.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.115.5, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.115.5\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Views\MainView.xaml.cs">
      <DependentUpon>MainView.xaml</DependentUpon>
    </Compile>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Views\MainView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="app.manifest" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App.Database\Alsi.App.Database.csproj">
      <Project>{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}</Project>
      <Name>Alsi.App.Database</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App.Desktop\Alsi.App.Desktop.csproj">
      <Project>{47a11109-4f55-41ce-9bd3-c74687d7a212}</Project>
      <Name>Alsi.App.Desktop</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App.Edge\Alsi.App.Edge.csproj">
      <Project>{9f3c92dc-e277-4374-b637-7c6df32fceb0}</Project>
      <Name>Alsi.App.Edge</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App\Alsi.App.csproj">
      <Project>{2bf46d86-9704-494a-8998-a478b601df80}</Project>
      <Name>Alsi.App</Name>
    </ProjectReference>
    <ProjectReference Include="..\Alsi.Tab.Plus.Web\Alsi.Tab.Plus.Web.csproj">
      <Project>{f09c0459-c89d-4973-b7f0-69c1a72a0e45}</Project>
      <Name>Alsi.Tab.Plus.Web</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.115.5\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.115.5\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.115.5\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.115.5\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
  </Target>
  <!-- 自动整理DLL到libs文件夹 -->
  <Target Name="OrganizeDllsToLibsFolder" AfterTargets="Build">
    <PropertyGroup>
      <!-- 根据不同配置确定正确的输出路径 -->
      <ActualOutputPath Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">$(MSBuildProjectDirectory)\..\bin\Debug\x64\</ActualOutputPath>
      <ActualOutputPath Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">$(MSBuildProjectDirectory)\..\bin\Release\x64\</ActualOutputPath>
      <LibsFolder>$(ActualOutputPath)libs</LibsFolder>
    </PropertyGroup>
    <Exec Command="PowerShell -Command &quot;Remove-Item -Recurse -Path '$(LibsFolder)'&quot;" />
    <!-- 创建libs子文件夹 -->
    <MakeDir Directories="$(LibsFolder)" Condition="!Exists('$(LibsFolder)')" />
    <Exec Command="PowerShell -Command &quot;Move-Item -Force -Path '$(ActualOutputPath)x64' -Destination '$(LibsFolder)'&quot;" />
    <Exec Command="PowerShell -Command &quot;Move-Item -Force -Path '$(ActualOutputPath)x86' -Destination '$(LibsFolder)'&quot;" />
    <!-- 定义需要移动到libs文件夹的第三方DLL -->
    <ItemGroup>
      <ThirdPartyDlls Include="$(ActualOutputPath)*.dll" />
    </ItemGroup>
    <!-- 移动第三方DLL到libs文件夹 -->
    <Move SourceFiles="@(ThirdPartyDlls)" DestinationFolder="$(LibsFolder)" Condition="Exists('%(Identity)')" />
    <!-- 输出信息 -->
    <Message Text="已将第三方DLL移动到libs文件夹: $(LibsFolder)" Importance="high" />
  </Target>
</Project>