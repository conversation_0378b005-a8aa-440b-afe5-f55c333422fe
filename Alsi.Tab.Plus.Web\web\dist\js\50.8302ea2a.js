"use strict";(self["webpackChunktab_plus_web"]=self["webpackChunktab_plus_web"]||[]).push([[50],{1050:function(e,a,l){l.r(a),l.d(a,{default:function(){return f}});l(8111),l(1701);var t=l(6768),n=l(4232),o=l(144),r=l(1219),c=l(2933),i=l(1021);const d={class:"app-flex-column"},u={class:"app-card app-space-between"},s={class:"app-card app-expand"};var p=(0,t.pM)({__name:"TestPlan",setup(e){const a=(0,o.KR)([]),l=(0,o.KR)(!1),p=(0,o.KR)(!1),m=(0,o.KR)(""),f=(0,o.Kh)({name:"",description:"",canoeConfig:[]}),g=(0,o.KR)([]),b={value:"value",label:"label",children:"children"},C={name:[{required:!0,message:"请输入名称",trigger:"blur"}],canoeConfig:[{required:!0,message:"请选择CANoe配置",trigger:"change"}]},k=(0,o.KR)(),v=(0,t.EW)(()=>p.value?"编辑测试计划":"创建测试计划"),_=async()=>{try{const e=await i.Ay.testPlan.getList();a.value=e.data}catch(e){r.nk.error("加载测试计划失败")}},y=async()=>{try{const e=await i.Ay.testPlan.getCanoeCfgOptions();g.value=e.data.map(e=>({value:e.category,label:e.category,children:e.configs.map(e=>({value:e,label:e}))}))}catch(e){r.nk.error("加载配置选项失败")}},w=()=>{p.value=!1,F(),l.value=!0},h=e=>{p.value=!0,m.value=e.id,f.name=e.name,f.description=e.description,f.canoeConfig=[e.canoeCfgCategory,e.canoeCfgName],l.value=!0},F=()=>{f.name="",f.description="",f.canoeConfig=[],k.value?.clearValidate()},V=async()=>{try{await k.value.validate();const[e,a]=f.canoeConfig;if(p.value){const l={id:m.value,name:f.name,description:f.description,canoeCfgCategory:e,canoeCfgName:a};await i.Ay.testPlan.update(l),r.nk.success("更新成功")}else{const l={name:f.name,description:f.description,canoeCfgCategory:e,canoeCfgName:a};await i.Ay.testPlan.create(l),r.nk.success("创建成功")}l.value=!1,_()}catch(e){r.nk.error(p.value?"更新失败":"创建失败")}},A=async e=>{try{await c.s.confirm("确定要删除这个测试计划吗？","确认删除",{type:"warning"}),await i.Ay.testPlan.delete(e.id),r.nk.success("删除成功"),_()}catch(a){"cancel"!==a&&r.nk.error("删除失败")}},W=e=>new Date(e).toLocaleString();return(0,t.sV)(()=>{_(),y()}),(e,o)=>{const r=(0,t.g2)("el-button"),c=(0,t.g2)("el-table-column"),i=(0,t.g2)("el-table"),p=(0,t.g2)("el-input"),m=(0,t.g2)("el-form-item"),_=(0,t.g2)("el-cascader"),y=(0,t.g2)("el-form"),F=(0,t.g2)("el-dialog");return(0,t.uX)(),(0,t.CE)("div",d,[(0,t.Lk)("div",u,[o[6]||(o[6]=(0,t.Lk)("h2",null,"测试计划",-1)),(0,t.bF)(r,{type:"primary",onClick:w},{default:(0,t.k6)(()=>o[5]||(o[5]=[(0,t.eW)("创建测试计划")])),_:1,__:[5]})]),(0,t.Lk)("div",s,[(0,t.bF)(i,{data:a.value,style:{width:"100%"}},{default:(0,t.k6)(()=>[(0,t.bF)(c,{prop:"name",label:"名称",width:"200"}),(0,t.bF)(c,{prop:"description",label:"描述"}),(0,t.bF)(c,{prop:"canoeCfgCategory",label:"CANoe CFG",width:"200"},{default:(0,t.k6)(e=>[(0,t.eW)((0,n.v_)(e.row.canoeCfgCategory+" / "+e.row.canoeCfgName),1)]),_:1}),(0,t.bF)(c,{prop:"creationTime",label:"创建时间",width:"180"},{default:(0,t.k6)(e=>[(0,t.eW)((0,n.v_)(W(e.row.creationTime)),1)]),_:1}),(0,t.bF)(c,{label:"操作",width:"200"},{default:(0,t.k6)(e=>[(0,t.bF)(r,{size:"small",onClick:a=>h(e.row)},{default:(0,t.k6)(()=>o[7]||(o[7]=[(0,t.eW)("编辑")])),_:2,__:[7]},1032,["onClick"]),(0,t.bF)(r,{size:"small",type:"danger",onClick:a=>A(e.row)},{default:(0,t.k6)(()=>o[8]||(o[8]=[(0,t.eW)("删除")])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),(0,t.bF)(F,{title:v.value,modelValue:l.value,"onUpdate:modelValue":o[4]||(o[4]=e=>l.value=e),width:"500px"},{footer:(0,t.k6)(()=>[(0,t.bF)(r,{onClick:o[3]||(o[3]=e=>l.value=!1)},{default:(0,t.k6)(()=>o[9]||(o[9]=[(0,t.eW)("取消")])),_:1,__:[9]}),(0,t.bF)(r,{type:"primary",onClick:V},{default:(0,t.k6)(()=>o[10]||(o[10]=[(0,t.eW)("确定")])),_:1,__:[10]})]),default:(0,t.k6)(()=>[(0,t.bF)(y,{model:f,rules:C,ref_key:"formRef",ref:k,"label-width":"120px"},{default:(0,t.k6)(()=>[(0,t.bF)(m,{label:"名称",prop:"name"},{default:(0,t.k6)(()=>[(0,t.bF)(p,{modelValue:f.name,"onUpdate:modelValue":o[0]||(o[0]=e=>f.name=e)},null,8,["modelValue"])]),_:1}),(0,t.bF)(m,{label:"描述",prop:"description"},{default:(0,t.k6)(()=>[(0,t.bF)(p,{modelValue:f.description,"onUpdate:modelValue":o[1]||(o[1]=e=>f.description=e),type:"textarea",rows:"3"},null,8,["modelValue"])]),_:1}),(0,t.bF)(m,{label:"CANoe配置",prop:"canoeConfig"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{modelValue:f.canoeConfig,"onUpdate:modelValue":o[2]||(o[2]=e=>f.canoeConfig=e),options:g.value,props:b,placeholder:"请选择配置",style:{width:"100%"}},null,8,["modelValue","options"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}});const m=p;var f=m}}]);
//# sourceMappingURL=50.8302ea2a.js.map