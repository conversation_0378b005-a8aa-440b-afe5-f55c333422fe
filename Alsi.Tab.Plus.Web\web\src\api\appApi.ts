import axios, { AxiosResponse } from 'axios';
import { sharedAppApi } from 'alsi-shared-web';

// 定义错误数据结构
export interface ErrorData {
  message: string;
  stack?: string;
  url: string;
  type?: string;
  vueHookInfo?: string; 
  codeInfo?: string;
}

// 定义应用信息接口
export interface AppInfo {
  dataFolder: string;
  logFolder: string;
}

// 定义测试模型接口
export interface TestMode {
  name: string;
}

// 数据日志转换相关接口
export enum DataLogFormat {
  Unknown = 0,
  Asc = 1,
  Blf = 2
}



export interface DataLogProcessRequest {
  sourceFilePath: string;
  targetFormat: DataLogFormat;
  enableSplit: boolean;
  splitFileCount: number;
}

export interface FileProgress {
  fileName: string;
  filePath: string;
  status: ProcessStatus;
  progressPercentage: number;
  errorMessage?: string;
}

export interface ProcessProgress {
  taskId: string;
  overallProgressPercentage: number;
  currentOperation: string;
  isCompleted: boolean;
  errorMessage?: string;
  fileProgresses: FileProgress[];
}

export enum ProcessStatus {
  Pending = "Pending",
  Processing = "Processing",
  Completed = "Completed",
  Failed = "Failed",
  Cancelled = "Cancelled"
}

// App Config 相关接口

export enum AppType {
  External = 'External',
  Internal = 'Internal'
}

export interface AppEntry {
  id: string;
  appType: AppType;
  exePath: string;
  appUrl: string;
  name: string;
  description: string;
  iconPath: string;
  tags: string[];
  showInTopMenu: boolean;
  showInBottomMenu: boolean;
  displayOrder: number;
  showInHomeCard: boolean;
  icon: string;
  exeExists: boolean;
  iconExists: boolean;
  fullExePath: string;
  workingDirectory: string;
}

export interface LaunchAppRequest {
  appId: string;
}

// CIN 参数工具相关接口
export interface CinTemplate {
  id: string;
  path: string;
  name: string;
  category: string;
  description: string;
  fullPath: string;
  fileExists: boolean;
}

export interface CaplVariable {
  name: string;
  type: string;
  isConst: boolean;
  isArray: boolean;
  arraySize: string;
  value: string;
  originalDeclaration: string;
  lineNumber: number;
  description?: string;
}

export interface CinParameterParseRequest {
  sourceType: string;
  templateId?: string;
  filePath: string;
}

export interface TypeDefinition {
  typeName: string;
  definition: string;
}

export interface CinParameterParseResponse {
  sourceFilePath: string;
  variables: CaplVariable[];
  typeDefinitions: TypeDefinition[];
}

export interface CinParameterRequest {
  sourceType: string;
  templateId?: string;
  filePath: string;
  parameterValues: { [key: string]: string };
}

// 源文件管理相关接口
export enum SourceFileType {
  Arxml = 'Arxml',
  Sddb = 'Sddb',
  Ldf = 'Ldf'
}

export enum SourceFileStatus {
  Pending = 'Pending',
  Parsing = 'Parsing',
  Parsed = 'Parsed',
  Error = 'Error'
}

export enum ParamType {
  String = 'String',
  Integer = 'Integer',
  Double = 'Double',
  Boolean = 'Boolean',
  Json = 'Json',
  Array = 'Array',
  Object = 'Object'
}

export interface SourceFile {
  id: string;
  path: string;
  fileName: string;
  fileType: SourceFileType;
  status: SourceFileStatus;
  parsedParams: ParsedParam[];
  addTime: Date;
  errorMessage: string;
}

export interface ParsedParam {
  ecuName: string;
  name: string;
  value: any;
  paramType: ParamType;
  source: string;
  description: string;
}

export interface AddSourceFilesRequest {
  filePaths: string[];
}

export interface ParseSourceFileRequest {
  fileId: string;
}

export interface RemoveSourceFileRequest {
  fileId: string;
}

export interface UserFileHistory {
  lastSelectedPaths: string[];
  lastUpdateTime: Date;
}

export interface CinParameterProcessResponse {
  outputFilePath: string;
}

const DATALOG_BASE_URL = '/api/DataLogConvert'
const APP_CONFIG_BASE_URL = '/api/AppConfig'
const CIN_PARAMETER_BASE_URL = '/api/CinParameter'

// 测试计划相关接口
export interface TestPlan {
  id: string;
  name: string;
  description: string;
  canoeCfgCategory: string;
  canoeCfgName: string;
  creationTime: Date;
  lastModificationTime?: Date;
}

export interface CreateTestPlanRequest {
  name: string;
  description: string;
  canoeCfgCategory: string;
  canoeCfgName: string;
}

export interface UpdateTestPlanRequest {
  id: string;
  name: string;
  description: string;
  canoeCfgCategory: string;
  canoeCfgName: string;
}

export interface CanoeCfgOption {
  category: string;
  configs: string[];
}

const TESTPLAN_BASE_URL = '/api/TestPlan'

export const appApi = {
  ...sharedAppApi,

  // 数据日志转换相关接口
  dataLogConvert: {
    // 选择文件
    selectFile(): Promise<AxiosResponse<string>> {
      return axios.post(`${DATALOG_BASE_URL}/select-file`);
    },

    // 开始处理
    startProcess(request: DataLogProcessRequest): Promise<AxiosResponse<string>> {
      return axios.post(`${DATALOG_BASE_URL}/start`, request);
    },

    // 获取进度
    getProgress(taskId: string): Promise<AxiosResponse<ProcessProgress>> {
      return axios.get(`${DATALOG_BASE_URL}/progress?taskId=${taskId}`);
    },

    // 取消处理
    cancelProcess(taskId: string): Promise<AxiosResponse<{ success: boolean }>> {
      return axios.post(`${DATALOG_BASE_URL}/cancel`, null, { params: { taskId } });
    }
  },

  // 应用配置相关接口
  appConfig: {
    // 获取应用列表
    getList(): Promise<AxiosResponse<AppEntry[]>> {
      return axios.get(`${APP_CONFIG_BASE_URL}/list`);
    },

    // 启动应用程序
    launch(request: LaunchAppRequest): Promise<AxiosResponse<{ success: boolean; message: string }>> {
      return axios.post(`${APP_CONFIG_BASE_URL}/launch`, request);
    },

    // 获取应用图标
    getIconUrl(appId: string): string {
      return `${APP_CONFIG_BASE_URL}/icon?appId=${appId}`;
    }
  },

  // CIN 参数工具相关接口
  cinParameter: {
    // 获取 CIN 模板列表
    getTemplates(): Promise<AxiosResponse<CinTemplate[]>> {
      return axios.get(`${CIN_PARAMETER_BASE_URL}/templates`);
    },

    // 选择 CIN 文件
    selectFile(): Promise<AxiosResponse<string>> {
      return axios.post(`${CIN_PARAMETER_BASE_URL}/select-file`);
    },

    // 解析 CIN 文件参数
    parseFile(request: CinParameterParseRequest): Promise<AxiosResponse<CinParameterParseResponse>> {
      return axios.post(`${CIN_PARAMETER_BASE_URL}/parse`, request);
    },

    // 处理 CIN 文件参数替换
    processFile(request: CinParameterRequest): Promise<AxiosResponse<CinParameterProcessResponse>> {
      return axios.post(`${CIN_PARAMETER_BASE_URL}/process`, request);
    },

    // 源文件管理相关接口

    // 选择源文件
    selectSourceFiles(): Promise<AxiosResponse<string[]>> {
      return axios.post(`${CIN_PARAMETER_BASE_URL}/select-source-files`);
    },

    // 添加源文件
    addSourceFiles(request: AddSourceFilesRequest): Promise<AxiosResponse<SourceFile[]>> {
      return axios.post(`${CIN_PARAMETER_BASE_URL}/add-source-files`, request);
    },

    // 获取所有源文件
    getSourceFiles(): Promise<AxiosResponse<SourceFile[]>> {
      return axios.get(`${CIN_PARAMETER_BASE_URL}/source-files`);
    },

    // 解析源文件参数
    parseSourceFile(request: ParseSourceFileRequest): Promise<AxiosResponse<SourceFile>> {
      return axios.post(`${CIN_PARAMETER_BASE_URL}/parse-source-file`, request);
    },

    // 移除源文件
    removeSourceFile(request: RemoveSourceFileRequest): Promise<AxiosResponse<{ success: boolean }>> {
      return axios.post(`${CIN_PARAMETER_BASE_URL}/remove-source-file`, request);
    },

    // 清空所有源文件
    clearSourceFiles(): Promise<AxiosResponse<{ success: boolean }>> {
      return axios.post(`${CIN_PARAMETER_BASE_URL}/clear-source-files`);
    },

    // 获取文件选择历史
    getFileHistory(): Promise<AxiosResponse<UserFileHistory>> {
      return axios.get(`${CIN_PARAMETER_BASE_URL}/file-history`);
    },

    // 清空文件选择历史
    clearFileHistory(): Promise<AxiosResponse<{ success: boolean }>> {
      return axios.post(`${CIN_PARAMETER_BASE_URL}/clear-file-history`);
    }
  },

  // 测试计划相关接口
  testPlan: {
    // 获取测试计划列表
    getList(): Promise<AxiosResponse<TestPlan[]>> {
      return axios.get(`${TESTPLAN_BASE_URL}/list`);
    },

    // 获取测试计划详情
    getDetail(id: string): Promise<AxiosResponse<TestPlan>> {
      return axios.get(`${TESTPLAN_BASE_URL}/detail?id=${id}`);
    },

    // 创建测试计划
    create(request: CreateTestPlanRequest): Promise<AxiosResponse<{ success: boolean }>> {
      return axios.post(`${TESTPLAN_BASE_URL}/create`, request);
    },

    // 更新测试计划
    update(request: UpdateTestPlanRequest): Promise<AxiosResponse<{ success: boolean }>> {
      return axios.post(`${TESTPLAN_BASE_URL}/update`, request);
    },

    // 删除测试计划
    delete(id: string): Promise<AxiosResponse<{ success: boolean }>> {
      return axios.post(`${TESTPLAN_BASE_URL}/delete`, { id });
    },

    // 获取CANoe配置选项
    getCanoeCfgOptions(): Promise<AxiosResponse<CanoeCfgOption[]>> {
      return axios.get(`${TESTPLAN_BASE_URL}/canoe-cfg-options`);
    }
  }
}

export default appApi
