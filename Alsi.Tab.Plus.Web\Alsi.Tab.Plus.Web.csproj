<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F09C0459-C89D-4973-B7F0-69C1A72A0E45}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Alsi.Tab.Plus.Web</RootNamespace>
    <AssemblyName>Alsi.Tab.Plus.Web</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controllers\TestPlanController.cs" />
    <Compile Include="Controllers\WebControllerBase.cs" />
    <Compile Include="Dto\TestPlanDto.cs" />
    <Compile Include="Mapping\MappingProfile.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TabPlusWebAssembly.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App\Alsi.App.csproj">
      <Project>{2bf46d86-9704-494a-8998-a478b601df80}</Project>
      <Name>Alsi.App</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.Common.Utils\Alsi.Common.Utils.csproj">
      <Project>{d45049c2-92f3-a823-ad5f-89ee4256d61b}</Project>
      <Name>Alsi.Common.Utils</Name>
    </ProjectReference>
    <ProjectReference Include="..\Alsi.Tab.Plus.Core\Alsi.Tab.Plus.Core.csproj">
      <Project>{98bd9299-ff77-4dd4-b5e9-ef73c812d58b}</Project>
      <Name>Alsi.Tab.Plus.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="build-web.ps1" />
    <EmbeddedResource Include="web.zip" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper">
      <Version>8.1.1</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.WebApi.Owin">
      <Version>5.3.0</Version>
    </PackageReference>
  </ItemGroup>
  <Target Name="MyPreBuildTask" BeforeTargets="PreBuildEvent">
    <Exec Command="powershell.exe -ExecutionPolicy Bypass -File &quot;$(ProjectDir)\build-web.ps1&quot;" />
  </Target>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>