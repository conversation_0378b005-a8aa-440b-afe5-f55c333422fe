<template>
  <div class="app-base">
    <div class="app-card-container">
      <router-view />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'App',
  components: {
  },
  setup() {
    return {
    };
  },
});
</script>

<style lang="scss">
/* 全局样式保持不变 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.main-content {
  flex: 1;
  background-color: var(--el-fill-color-base);
  overflow-y: auto;
  transition: margin-left 0.3s ease;
}
</style>
