﻿using Alsi.App;
using Alsi.App.Database;
using Alsi.App.Desktop;
using Alsi.App.Desktop.Utils;
using Alsi.App.Edge;
using Alsi.Tab.Plus.Views;
using Alsi.Tab.Plus.Web;
using System.ComponentModel;
using System.Windows;

namespace Alsi.Tab.Plus
{
    public partial class App : Application
    {
        private WebHostApp _webHostApp;

        private void Application_Startup(object sender, StartupEventArgs e)
        {
            var tabPlusWebAssembly = new TabPlusWebAssembly();
            _webHostApp = WebHostApp
                .Create(appName: "TabPlus", appFolderName: "Alsi.Atts", productFolderName: "TabPlus")
                .UseAppExceptionHandler(this)
                .UseSerilog("TabPlus")
                .UseEdge()
                .UseFreeSql("TabPlus.sqlite")
                .UseApiHost(tabPlusWebAssembly)
                .Build();

            var mainWindow = WindowUtils.ShowWindow<MainView>("TAB Plus", null, size: new Size(1200, 750));
            mainWindow.MinWidth = 800;
            mainWindow.MinHeight = 600;

            Current.MainWindow = mainWindow;
            Current.MainWindow.Closing += MainWindow_Closing;
        }

        private void MainWindow_Closing(object sender, CancelEventArgs e)
        {
            e.Cancel = true;

            if (_webHostApp != null)
            {
                _webHostApp.Exit();
            }
        }
    }
}
