using Alsi.Common.Utils;
using Alsi.Tab.Plus.Core.Models;
using Alsi.Tab.Plus.Core.Services;
using Alsi.Tab.Plus.Web.Dto;
using System;
using System.Linq;
using System.Web.Http;

namespace Alsi.Tab.Plus.Web.Controllers
{
    public class TestPlanController : WebControllerBase
    {
        private readonly TestPlanService _testPlanService;

        public TestPlanController()
        {
            _testPlanService = new TestPlanService();
        }

        [HttpGet]
        [ActionName("list")]
        public IHttpActionResult GetTestPlans()
        {
            var testPlans = _testPlanService.GetTestPlans();
            var dtos = Mapper.Map<TestPlan[], TestPlanDto[]>(testPlans);
            return Ok(dtos);
        }

        [HttpGet]
        [ActionName("detail")]
        public IHttpActionResult GetTestPlan(Guid id)
        {
            var testPlans = _testPlanService.GetTestPlans();
            var testPlan = testPlans.FirstOrDefault(x => x.Id == id);
            if (testPlan == null)
            {
                throw new AppException($"Can't find test plan: {id}");
            }

            var dto = Mapper.Map<TestPlan, TestPlanDto>(testPlan);
            return Ok(dto);
        }

        [HttpPost]
        [ActionName("create")]
        public IHttpActionResult CreateTestPlan(CreateTestPlanRequest request)
        {
            _testPlanService.CreateTestPlan(request.Name, request.Description,
                request.CanoeCfgCategory, request.CanoeCfgName);
            return Ok();
        }

        [HttpPost]
        [ActionName("update")]
        public IHttpActionResult UpdateTestPlan(UpdateTestPlanRequest request)
        {
            _testPlanService.UpdateTestPlan(request.Id, request.Name, request.Description);
            return Ok();
        }

        [HttpPost]
        [ActionName("delete")]
        public IHttpActionResult DeleteTestPlan(DeleteTestPlanRequest request)
        {
            _testPlanService.DeleteTestPlan(request.Id);
            return Ok();
        }

        [HttpGet]
        [ActionName("canoe-cfg-options")]
        public IHttpActionResult GetCanoeCfgOptions()
        {
            var options = _testPlanService.GetCanoeCfgOptions();
            return Ok(options);
        }
    }
}