{"files": [{"time": "2025-07-15T11:04:59.9020353+08:00", "path": ".browserslistrc"}, {"time": "2025-07-15T11:20:58.7312504+08:00", "path": ".eslintrc.js"}, {"time": "2025-07-15T11:04:59.9050345+08:00", "path": ".giti<PERSON>re"}, {"time": "2025-07-15T11:05:25.9128438+08:00", "path": "babel.config.js"}, {"time": "2025-07-16T10:39:05.5478702+08:00", "path": "package.json"}, {"time": "2025-07-15T11:05:26.0618435+08:00", "path": "README.md"}, {"time": "2025-07-15T11:04:59.9110352+08:00", "path": "tsconfig.json"}, {"time": "2025-07-15T11:05:25.9128438+08:00", "path": "vue.config.js"}, {"time": "2025-07-16T10:39:05.5368242+08:00", "path": "yarn.lock"}, {"time": "2025-07-15T11:04:59.9060362+08:00", "path": "public\\favicon.ico"}, {"time": "2025-07-15T11:04:59.9070335+08:00", "path": "public\\index.html"}, {"time": "2025-07-16T10:54:35.8359223+08:00", "path": "src\\App.vue"}, {"time": "2025-07-15T14:48:35.3025502+08:00", "path": "src\\main.ts"}, {"time": "2025-07-15T11:04:59.9120336+08:00", "path": "src\\shims-vue.d.ts"}, {"time": "2025-07-15T13:25:11.9655587+08:00", "path": "src\\alsi-shared-web\\package.json"}, {"time": "2025-07-15T14:56:56.9647874+08:00", "path": "src\\alsi-shared-web\\src\\index.ts"}, {"time": "2025-07-15T14:56:53.9412451+08:00", "path": "src\\alsi-shared-web\\src\\api\\sharedAppApi.ts"}, {"time": "2025-07-15T13:47:07.2585379+08:00", "path": "src\\alsi-shared-web\\src\\components\\MainMenu.vue"}, {"time": "2025-07-16T10:45:26.0940728+08:00", "path": "src\\alsi-shared-web\\src\\styles\\shared-variables.css"}, {"time": "2025-07-03T08:04:04.5084648+08:00", "path": "src\\alsi-shared-web\\src\\types\\element-plus.d.ts"}, {"time": "2025-07-03T08:04:04.4854668+08:00", "path": "src\\alsi-shared-web\\src\\utils\\errorHandler.ts"}, {"time": "2025-07-15T16:10:56.1262307+08:00", "path": "src\\api\\appApi.ts"}, {"time": "2025-07-03T08:04:04.4934838+08:00", "path": "src\\assets\\logo.svg"}, {"time": "2025-07-16T10:17:24.5749953+08:00", "path": "src\\router\\index.ts"}, {"time": "2025-07-15T11:05:25.9128438+08:00", "path": "src\\store\\index.ts"}, {"time": "2025-07-16T10:55:25.8849724+08:00", "path": "src\\styles\\element-variables.css"}, {"time": "2025-07-09T14:26:43.2584270+08:00", "path": "src\\utils\\cinFormatter.ts"}, {"time": "2025-07-15T13:23:21.9696957+08:00", "path": "src\\utils\\errorHandler.ts"}, {"time": "2025-07-16T10:56:15.8039190+08:00", "path": "src\\views\\TestPlan.vue"}], "buildTime": "2025-07-16T10:57:14.2228067+08:00"}