{"version": 3, "targets": {".NETFramework,Version=v4.6.2": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "FreeSql/3.5.106": {"type": "package", "compile": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}}, "FreeSql.DbContext/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106"}, "compile": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}}, "FreeSql.Provider.Sqlite/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106", "System.Data.SQLite.Core": "*********"}, "compile": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}, "runtime": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "Stub.System.Data.SQLite.Core.NetFramework/*********": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Data.SQLite.Core/*********": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[*********]"}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"AutoMapper": "8.1.1", "Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Database/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "FreeSql.DbContext": "3.5.106", "FreeSql.Provider.Sqlite": "3.5.106"}, "compile": {"bin/placeholder/Alsi.App.Database.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Database.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}}, ".NETFramework,Version=v4.6.2/win": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "FreeSql/3.5.106": {"type": "package", "compile": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}}, "FreeSql.DbContext/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106"}, "compile": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}}, "FreeSql.Provider.Sqlite/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106", "System.Data.SQLite.Core": "*********"}, "compile": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}, "runtime": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "Stub.System.Data.SQLite.Core.NetFramework/*********": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Data.SQLite.Core/*********": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[*********]"}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"AutoMapper": "8.1.1", "Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Database/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "FreeSql.DbContext": "3.5.106", "FreeSql.Provider.Sqlite": "3.5.106"}, "compile": {"bin/placeholder/Alsi.App.Database.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Database.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}}, ".NETFramework,Version=v4.6.2/win-arm64": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "FreeSql/3.5.106": {"type": "package", "compile": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}}, "FreeSql.DbContext/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106"}, "compile": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}}, "FreeSql.Provider.Sqlite/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106", "System.Data.SQLite.Core": "*********"}, "compile": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}, "runtime": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "Stub.System.Data.SQLite.Core.NetFramework/*********": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Data.SQLite.Core/*********": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[*********]"}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"AutoMapper": "8.1.1", "Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Database/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "FreeSql.DbContext": "3.5.106", "FreeSql.Provider.Sqlite": "3.5.106"}, "compile": {"bin/placeholder/Alsi.App.Database.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Database.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}}, ".NETFramework,Version=v4.6.2/win-x64": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "FreeSql/3.5.106": {"type": "package", "compile": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}}, "FreeSql.DbContext/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106"}, "compile": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}}, "FreeSql.Provider.Sqlite/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106", "System.Data.SQLite.Core": "*********"}, "compile": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}, "runtime": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "Stub.System.Data.SQLite.Core.NetFramework/*********": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Data.SQLite.Core/*********": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[*********]"}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"AutoMapper": "8.1.1", "Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Database/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "FreeSql.DbContext": "3.5.106", "FreeSql.Provider.Sqlite": "3.5.106"}, "compile": {"bin/placeholder/Alsi.App.Database.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Database.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}}, ".NETFramework,Version=v4.6.2/win-x86": {"AutoMapper/8.1.1": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/AutoMapper.dll": {"related": ".pdb;.xml"}}}, "FreeSql/3.5.106": {"type": "package", "compile": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net451/FreeSql.dll": {"related": ".pdb;.xml"}}}, "FreeSql.DbContext/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106"}, "compile": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/FreeSql.DbContext.dll": {"related": ".pdb;.xml"}}}, "FreeSql.Provider.Sqlite/3.5.106": {"type": "package", "dependencies": {"FreeSql": "3.5.106", "System.Data.SQLite.Core": "*********"}, "compile": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}, "runtime": {"lib/net45/FreeSql.Provider.Sqlite.dll": {"related": ".pdb"}}}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "Newtonsoft.Json.Bson": "1.0.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Net.Http.Formatting.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Client": "6.0.0"}, "compile": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Core": "5.3.0", "Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/System.Web.Http.Owin.dll": {"related": ".xml"}}}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"type": "package", "dependencies": {"Microsoft.AspNet.WebApi.Owin": "5.3.0", "Microsoft.Owin.Host.HttpListener": "4.2.2", "Microsoft.Owin.Hosting": "4.2.2"}}, "Microsoft.Owin/4.2.2": {"type": "package", "dependencies": {"Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.dll": {"related": ".xml"}}}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Host.HttpListener.dll": {"related": ".xml"}}}, "Microsoft.Owin.Hosting/4.2.2": {"type": "package", "dependencies": {"Microsoft.Owin": "4.2.2", "Owin": "1.0.0"}, "compile": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Microsoft.Owin.Hosting.dll": {"related": ".xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "Owin/1.0.0": {"type": "package", "compile": {"lib/net40/Owin.dll": {}}, "runtime": {"lib/net40/Owin.dll": {}}}, "Serilog/2.10.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net46/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/Serilog.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "SharpZipLib/1.4.2": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.2"}, "compile": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ICSharpCode.SharpZipLib.dll": {"related": ".pdb;.xml"}}}, "Stub.System.Data.SQLite.Core.NetFramework/*********": {"type": "package", "compile": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "runtime": {"lib/net46/System.Data.SQLite.dll": {"related": ".dll.altconfig;.xml"}}, "build": {"buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets": {}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Data.SQLite.Core/*********": {"type": "package", "dependencies": {"Stub.System.Data.SQLite.Core.NetFramework": "[*********]"}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "Alsi.App/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"AutoMapper": "8.1.1", "Microsoft.AspNet.WebApi.OwinSelfHost": "5.3.0", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SharpZipLib": "1.4.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"bin/placeholder/Alsi.App.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.dll": {}}}, "Alsi.App.Database/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Alsi.App": "1.0.0", "FreeSql.DbContext": "3.5.106", "FreeSql.Provider.Sqlite": "3.5.106"}, "compile": {"bin/placeholder/Alsi.App.Database.dll": {}}, "runtime": {"bin/placeholder/Alsi.App.Database.dll": {}}}, "Alsi.Common.Utils/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.6.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/Alsi.Common.Utils.dll": {}}, "runtime": {"bin/placeholder/Alsi.Common.Utils.dll": {}}}}}, "libraries": {"AutoMapper/8.1.1": {"sha512": "GKBliBukkLG0z/U0T4Jy+yEpr5bKDImQ9nEy/T8jqNulwNkWDjiEWa9TL4yCxIhleDbmVmw2xYSaJ3Eb2OcSUg==", "type": "package", "path": "automapper/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "automapper.8.1.1.nupkg.sha512", "automapper.nuspec", "lib/net461/AutoMapper.dll", "lib/net461/AutoMapper.pdb", "lib/net461/AutoMapper.xml", "lib/netstandard2.0/AutoMapper.dll", "lib/netstandard2.0/AutoMapper.pdb", "lib/netstandard2.0/AutoMapper.xml"]}, "FreeSql/3.5.106": {"sha512": "PgQuwtsjz3CH3M92ViEyRCej7og0X0xM+Of707zW2SW9GGIQqmHOkCrdfNTnmUb9vY1W+juOjp92WwS3j/8/rg==", "type": "package", "path": "freesql/3.5.106", "files": [".nupkg.metadata", ".signature.p7s", "freesql.3.5.106.nupkg.sha512", "freesql.nuspec", "lib/net40/FreeSql.dll", "lib/net40/FreeSql.pdb", "lib/net40/FreeSql.xml", "lib/net45/FreeSql.dll", "lib/net45/FreeSql.pdb", "lib/net45/FreeSql.xml", "lib/net451/FreeSql.dll", "lib/net451/FreeSql.pdb", "lib/net451/FreeSql.xml", "lib/netstandard2.0/FreeSql.dll", "lib/netstandard2.0/FreeSql.pdb", "lib/netstandard2.0/FreeSql.xml", "lib/netstandard2.1/FreeSql.dll", "lib/netstandard2.1/FreeSql.pdb", "lib/netstandard2.1/FreeSql.xml", "logo.png", "readme.md"]}, "FreeSql.DbContext/3.5.106": {"sha512": "LFwkMV10gmqKO9NEF1ghPqW815u8jFpMDNnrcG3ZteS9eihxKdCquMSZxdbtqjTSpHc4TW0qhPxZviQHnfh/sA==", "type": "package", "path": "freesql.dbcontext/3.5.106", "files": [".nupkg.metadata", ".signature.p7s", "freesql.dbcontext.3.5.106.nupkg.sha512", "freesql.dbcontext.nuspec", "lib/net40/FreeSql.DbContext.dll", "lib/net40/FreeSql.DbContext.pdb", "lib/net40/FreeSql.DbContext.xml", "lib/net45/FreeSql.DbContext.dll", "lib/net45/FreeSql.DbContext.pdb", "lib/net45/FreeSql.DbContext.xml", "lib/net5.0/FreeSql.DbContext.dll", "lib/net5.0/FreeSql.DbContext.pdb", "lib/net5.0/FreeSql.DbContext.xml", "lib/net6.0/FreeSql.DbContext.dll", "lib/net6.0/FreeSql.DbContext.pdb", "lib/net6.0/FreeSql.DbContext.xml", "lib/net7.0/FreeSql.DbContext.dll", "lib/net7.0/FreeSql.DbContext.pdb", "lib/net7.0/FreeSql.DbContext.xml", "lib/net8.0/FreeSql.DbContext.dll", "lib/net8.0/FreeSql.DbContext.pdb", "lib/net8.0/FreeSql.DbContext.xml", "lib/net9.0/FreeSql.DbContext.dll", "lib/net9.0/FreeSql.DbContext.pdb", "lib/net9.0/FreeSql.DbContext.xml", "lib/netcoreapp3.1/FreeSql.DbContext.dll", "lib/netcoreapp3.1/FreeSql.DbContext.pdb", "lib/netcoreapp3.1/FreeSql.DbContext.xml", "lib/netstandard2.0/FreeSql.DbContext.dll", "lib/netstandard2.0/FreeSql.DbContext.pdb", "lib/netstandard2.0/FreeSql.DbContext.xml", "lib/netstandard2.1/FreeSql.DbContext.dll", "lib/netstandard2.1/FreeSql.DbContext.pdb", "lib/netstandard2.1/FreeSql.DbContext.xml", "logo.png", "readme.md"]}, "FreeSql.Provider.Sqlite/3.5.106": {"sha512": "tcLM85BNndoPl0ZTiBbn5yzz/ysYn+6YsVh7n4kHxTtDTTfUbSu5H1C4ed+fbjmknS+A2e2WwdM8iKq1lqRhBg==", "type": "package", "path": "freesql.provider.sqlite/3.5.106", "files": [".nupkg.metadata", ".signature.p7s", "freesql.provider.sqlite.3.5.106.nupkg.sha512", "freesql.provider.sqlite.nuspec", "lib/net40/FreeSql.Provider.Sqlite.dll", "lib/net40/FreeSql.Provider.Sqlite.pdb", "lib/net45/FreeSql.Provider.Sqlite.dll", "lib/net45/FreeSql.Provider.Sqlite.pdb", "lib/netstandard2.0/FreeSql.Provider.Sqlite.dll", "lib/netstandard2.0/FreeSql.Provider.Sqlite.pdb", "logo.png", "readme.md"]}, "Microsoft.AspNet.WebApi.Client/6.0.0": {"sha512": "zXeWP03dTo67AoDHUzR+/urck0KFssdCKOC+dq7Nv1V2YbFh/nIg09L0/3wSvyRONEdwxGB/ssEGmPNIIhAcAw==", "type": "package", "path": "microsoft.aspnet.webapi.client/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "NET.icon.png", "NET_Library_EULA_ENU.txt", "lib/net45/System.Net.Http.Formatting.dll", "lib/net45/System.Net.Http.Formatting.xml", "lib/netstandard1.3/System.Net.Http.Formatting.dll", "lib/netstandard1.3/System.Net.Http.Formatting.xml", "lib/netstandard2.0/System.Net.Http.Formatting.dll", "lib/netstandard2.0/System.Net.Http.Formatting.xml", "microsoft.aspnet.webapi.client.6.0.0.nupkg.sha512", "microsoft.aspnet.webapi.client.nuspec"]}, "Microsoft.AspNet.WebApi.Core/5.3.0": {"sha512": "h0oLsUFPgoB1R+6ichy1bniAs4oC6w6XrPsEgn+LuQBxBGskN0djSOSX7hzL8LTFEZUTdsh/3ShjRu1Mb2QRfw==", "type": "package", "path": "microsoft.aspnet.webapi.core/5.3.0", "files": [".nupkg.metadata", ".signature.p7s", "Content/web.config.transform", "NET.icon.png", "NET_Library_EULA_ENU.txt", "lib/net45/System.Web.Http.dll", "lib/net45/System.Web.Http.xml", "microsoft.aspnet.webapi.core.5.3.0.nupkg.sha512", "microsoft.aspnet.webapi.core.nuspec"]}, "Microsoft.AspNet.WebApi.Owin/5.3.0": {"sha512": "goLNSAd5Vzp6nKlCueU9IeV4HbqCYYhl2qjAPNJcvyoK1W3uq3AQeebRuFZeQ5zJG9+ACG5jCpOhEu98gw79hg==", "type": "package", "path": "microsoft.aspnet.webapi.owin/5.3.0", "files": [".nupkg.metadata", ".signature.p7s", "NET.icon.png", "NET_Library_EULA_ENU.txt", "lib/net45/System.Web.Http.Owin.dll", "lib/net45/System.Web.Http.Owin.xml", "microsoft.aspnet.webapi.owin.5.3.0.nupkg.sha512", "microsoft.aspnet.webapi.owin.nuspec"]}, "Microsoft.AspNet.WebApi.OwinSelfHost/5.3.0": {"sha512": "a/3+IJGF3N1LYF6A9esGq4xR7SIl+fpLvcgC0PrQuTtl5k1W4FLtC/qOGLi6n46QgaKf18jsm0sknImRkyuZgg==", "type": "package", "path": "microsoft.aspnet.webapi.owinselfhost/5.3.0", "files": [".nupkg.metadata", ".signature.p7s", "NET.icon.png", "NET_Library_EULA_ENU.txt", "microsoft.aspnet.webapi.owinselfhost.5.3.0.nupkg.sha512", "microsoft.aspnet.webapi.owinselfhost.nuspec"]}, "Microsoft.Owin/4.2.2": {"sha512": "jt410l/8dvCIguRdU7dupYdm4kGLepVdD8EOTKU4nYZcLRrn6kQYqI6pbJOTJp7Vlm/T2WUF5bzyKK2z29xtjg==", "type": "package", "path": "microsoft.owin/4.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net45/Microsoft.Owin.dll", "lib/net45/Microsoft.Owin.xml", "microsoft.owin.4.2.2.nupkg.sha512", "microsoft.owin.nuspec"]}, "Microsoft.Owin.Host.HttpListener/4.2.2": {"sha512": "Kl1A0sBzfMD3qvX6XcGU0FopN6POFFRpIEQnKIAbvsShadIG9/UxgDdHVlX/IzFHXwIHfY59Ae4RGDVKYNvIqQ==", "type": "package", "path": "microsoft.owin.host.httplistener/4.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net45/Microsoft.Owin.Host.HttpListener.dll", "lib/net45/Microsoft.Owin.Host.HttpListener.xml", "microsoft.owin.host.httplistener.4.2.2.nupkg.sha512", "microsoft.owin.host.httplistener.nuspec"]}, "Microsoft.Owin.Hosting/4.2.2": {"sha512": "KsupM0TNPUfLn1uHvQy22IX6VWE+wi7C2shseSnhO9JYFxgwWcsSmjxrRpw+xcD+4hA3O280tBxfZ6T+kJuhjg==", "type": "package", "path": "microsoft.owin.hosting/4.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net45/Microsoft.Owin.Hosting.dll", "lib/net45/Microsoft.Owin.Hosting.xml", "microsoft.owin.hosting.4.2.2.nupkg.sha512", "microsoft.owin.hosting.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Newtonsoft.Json.Bson/1.0.2": {"sha512": "QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "type": "package", "path": "newtonsoft.json.bson/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net45/Newtonsoft.Json.Bson.dll", "lib/net45/Newtonsoft.Json.Bson.pdb", "lib/net45/Newtonsoft.Json.Bson.xml", "lib/netstandard1.3/Newtonsoft.Json.Bson.dll", "lib/netstandard1.3/Newtonsoft.Json.Bson.pdb", "lib/netstandard1.3/Newtonsoft.Json.Bson.xml", "lib/netstandard2.0/Newtonsoft.Json.Bson.dll", "lib/netstandard2.0/Newtonsoft.Json.Bson.pdb", "lib/netstandard2.0/Newtonsoft.Json.Bson.xml", "newtonsoft.json.bson.1.0.2.nupkg.sha512", "newtonsoft.json.bson.nuspec"]}, "Owin/1.0.0": {"sha512": "OseTFniKmyp76mEzOBwIKGBRS5eMoYNkMKaMXOpxx9jv88+b6mh1rSaw43vjBOItNhaLFG3d0a20PfHyibH5sw==", "type": "package", "path": "owin/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Owin.dll", "owin.1.0.0.nupkg.sha512", "owin.nuspec"]}, "Serilog/2.10.0": {"sha512": "+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "type": "package", "path": "serilog/2.10.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.dll", "lib/net45/Serilog.xml", "lib/net46/Serilog.dll", "lib/net46/Serilog.xml", "lib/netstandard1.0/Serilog.dll", "lib/netstandard1.0/Serilog.xml", "lib/netstandard1.3/Serilog.dll", "lib/netstandard1.3/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "lib/netstandard2.1/Serilog.dll", "lib/netstandard2.1/Serilog.xml", "serilog.2.10.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.Sinks.Console/4.1.0": {"sha512": "K6N5q+5fetjnJPvCmkWOpJ/V8IEIoMIB1s86OzBrbxwTyHxdx3pmz4H+8+O/Dc/ftUX12DM1aynx/dDowkwzqg==", "type": "package", "path": "serilog.sinks.console/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Console.dll", "lib/net45/Serilog.Sinks.Console.xml", "lib/net5.0/Serilog.Sinks.Console.dll", "lib/net5.0/Serilog.Sinks.Console.xml", "lib/netstandard1.3/Serilog.Sinks.Console.dll", "lib/netstandard1.3/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.4.1.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.File/5.0.0": {"sha512": "uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "type": "package", "path": "serilog.sinks.file/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net45/Serilog.Sinks.File.dll", "lib/net45/Serilog.Sinks.File.pdb", "lib/net45/Serilog.Sinks.File.xml", "lib/net5.0/Serilog.Sinks.File.dll", "lib/net5.0/Serilog.Sinks.File.pdb", "lib/net5.0/Serilog.Sinks.File.xml", "lib/netstandard1.3/Serilog.Sinks.File.dll", "lib/netstandard1.3/Serilog.Sinks.File.pdb", "lib/netstandard1.3/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.pdb", "lib/netstandard2.0/Serilog.Sinks.File.xml", "lib/netstandard2.1/Serilog.Sinks.File.dll", "lib/netstandard2.1/Serilog.Sinks.File.pdb", "lib/netstandard2.1/Serilog.Sinks.File.xml", "serilog.sinks.file.5.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "SharpZipLib/1.4.2": {"sha512": "yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "type": "package", "path": "sharpziplib/1.4.2", "files": [".nupkg.metadata", ".signature.p7s", "images/sharpziplib-nuget-256x256.png", "lib/net6.0/ICSharpCode.SharpZipLib.dll", "lib/net6.0/ICSharpCode.SharpZipLib.pdb", "lib/net6.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.0/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.0/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.0/ICSharpCode.SharpZipLib.xml", "lib/netstandard2.1/ICSharpCode.SharpZipLib.dll", "lib/netstandard2.1/ICSharpCode.SharpZipLib.pdb", "lib/netstandard2.1/ICSharpCode.SharpZipLib.xml", "sharpziplib.1.4.2.nupkg.sha512", "sharpziplib.nuspec"]}, "Stub.System.Data.SQLite.Core.NetFramework/*********": {"sha512": "fXkWBM+Dstt1JEItGsVyOJvY+UrnPaT7NCrWGyGuAxzF7ceKNsDBTjR1yrPOwUrQ/V9R6moHdB2FiO0+j94m6g==", "type": "package", "path": "stub.system.data.sqlite.core.netframework/*********", "files": [".nupkg.metadata", ".signature.p7s", "build/net20/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net20/x64/SQLite.Interop.dll", "build/net20/x86/SQLite.Interop.dll", "build/net40/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net40/x64/SQLite.Interop.dll", "build/net40/x86/SQLite.Interop.dll", "build/net45/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net45/x64/SQLite.Interop.dll", "build/net45/x86/SQLite.Interop.dll", "build/net451/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net451/x64/SQLite.Interop.dll", "build/net451/x86/SQLite.Interop.dll", "build/net46/Stub.System.Data.SQLite.Core.NetFramework.targets", "build/net46/x64/SQLite.Interop.dll", "build/net46/x86/SQLite.Interop.dll", "buildTransitive/net20/Stub.System.Data.SQLite.Core.NetFramework.targets", "buildTransitive/net40/Stub.System.Data.SQLite.Core.NetFramework.targets", "buildTransitive/net45/Stub.System.Data.SQLite.Core.NetFramework.targets", "buildTransitive/net451/Stub.System.Data.SQLite.Core.NetFramework.targets", "buildTransitive/net46/Stub.System.Data.SQLite.Core.NetFramework.targets", "lib/net20/System.Data.SQLite.dll", "lib/net20/System.Data.SQLite.dll.altconfig", "lib/net20/System.Data.SQLite.xml", "lib/net40/System.Data.SQLite.dll", "lib/net40/System.Data.SQLite.dll.altconfig", "lib/net40/System.Data.SQLite.xml", "lib/net45/System.Data.SQLite.dll", "lib/net45/System.Data.SQLite.dll.altconfig", "lib/net45/System.Data.SQLite.xml", "lib/net451/System.Data.SQLite.dll", "lib/net451/System.Data.SQLite.dll.altconfig", "lib/net451/System.Data.SQLite.xml", "lib/net46/System.Data.SQLite.dll", "lib/net46/System.Data.SQLite.dll.altconfig", "lib/net46/System.Data.SQLite.xml", "stub.system.data.sqlite.core.netframework.*********.nupkg.sha512", "stub.system.data.sqlite.core.netframework.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Data.SQLite.Core/*********": {"sha512": "vADIqqgpxaC5xR6qOV8/KMZkQeSDCfmmWpVOtQx0oEr3Yjq2XdTxX7+jfE4+oO2xPovAbYiz6Q5cLRbSsDkq6Q==", "type": "package", "path": "system.data.sqlite.core/*********", "files": [".nupkg.metadata", ".signature.p7s", "system.data.sqlite.core.*********.nupkg.sha512", "system.data.sqlite.core.nuspec"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"sha512": "YkOfl8PsmWT4ASkkEFFlfajgwomK8VnhwOIx0JEego69Tw5IqXjbzUBwNKcE5KprqlK92ZCYT56nQwmyEv45Ug==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "lib/net462/System.Runtime.CompilerServices.Unsafe.dll", "lib/net462/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/net7.0/_._", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.1.1.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Alsi.App/1.0.0": {"type": "project", "path": "../../Alsi.Common/Alsi.App/Alsi.App.csproj", "msbuildProject": "../../Alsi.Common/Alsi.App/Alsi.App.csproj"}, "Alsi.App.Database/1.0.0": {"type": "project", "path": "../../Alsi.Common/Alsi.App.Database/Alsi.App.Database.csproj", "msbuildProject": "../../Alsi.Common/Alsi.App.Database/Alsi.App.Database.csproj"}, "Alsi.Common.Utils/1.0.0": {"type": "project", "path": "../../Alsi.Common/Alsi.Common.Utils/Alsi.Common.Utils.csproj", "msbuildProject": "../../Alsi.Common/Alsi.Common.Utils/Alsi.Common.Utils.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.6.2": ["Alsi.App.Database >= 1.0.0", "Alsi.Common.Utils >= 1.0.0", "FreeSql >= 3.5.106"]}, "packageFolders": {"D:\\nuget_packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Core\\Alsi.Tab.Plus.Core.csproj", "projectName": "Alsi.Tab.Plus.Core", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Core\\Alsi.Tab.Plus.Core.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Plus\\Alsi.Tab.Plus.Core\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\Alsi.App.Database.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\Alsi.App.Database.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net462": {"dependencies": {"FreeSql": {"target": "Package", "version": "[3.5.106, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}